# 规则参数合并功能优化说明

## 🔧 问题描述

原有的规则详情查询存在逻辑缺陷：
- 当新增共享参数后，已有的规则表达式无法使用这些新参数
- 规则详情只显示已使用的参数，无法看到可用的新参数
- 用户在修改规则时无法知道有哪些新参数可以使用

## ✅ 解决方案

### 参数合并策略

实现了智能的参数合并机制：
```
规则参数列表 = 已使用的参数 + 共享参数池中未使用的参数
```

### 核心优化

#### 1. **参数合并方法**

```java
/**
 * 合并规则参数：已使用的参数 + 共享参数池中未使用的参数
 */
private List<AuditParamDto> mergeRuleParameters(Long ruleId) {
    // 1. 查询规则已使用的参数
    List<RcBlackListRuleParamEntity> usedParams = rcBlackListRuleParamEntityRepository.findByRuleIds(Arrays.asList(ruleId));
    
    // 2. 查询所有共享参数
    List<RcParamEntity> allSharedParams = rcParamEntityRepository.findAll();
    
    // 3. 获取已使用参数的名称集合
    Set<String> usedParamNames = usedParams.stream()
            .map(RcBlackListRuleParamEntity::getParamName)
            .collect(Collectors.toSet());
    
    // 4. 转换已使用的参数
    List<AuditParamDto> resultList = new ArrayList<>();
    for (RcBlackListRuleParamEntity usedParam : usedParams) {
        AuditParamDto dto = convertToAuditParamDto(usedParam);
        dto.setUsed(true); // 标记为已使用
        resultList.add(dto);
    }
    
    // 5. 添加共享参数池中未使用的参数
    for (RcParamEntity sharedParam : allSharedParams) {
        if (!usedParamNames.contains(sharedParam.getParamName())) {
            AuditParamDto dto = convertToAuditParamDto(sharedParam);
            dto.setUsed(false); // 标记为未使用
            resultList.add(dto);
        }
    }
    
    // 6. 按参数名称排序，已使用的参数排在前面
    resultList.sort((a, b) -> {
        if (a.getUsed() && !b.getUsed()) {
            return -1; // a 排在前面
        } else if (!a.getUsed() && b.getUsed()) {
            return 1; // b 排在前面
        } else {
            return a.getParamName().compareTo(b.getParamName()); // 按名称排序
        }
    });
    
    return resultList;
}
```

#### 2. **DTO 增强**

为 `AuditParamDto` 添加了新字段：

```java
@ApiModelProperty(value = "是否已被规则使用")
private Boolean used;

@ApiModelProperty(value = "规则参数关联ID（仅已使用参数有值）")
private Long ruleParamId;
```

#### 3. **参数转换方法**

```java
/**
 * 转换规则参数实体为DTO
 */
private AuditParamDto convertToAuditParamDto(RcBlackListRuleParamEntity ruleParam) {
    AuditParamDto dto = new AuditParamDto();
    dto.setParamId(ruleParam.getId());
    dto.setParamName(ruleParam.getParamName());
    dto.setParamType(ruleParam.getParamType());
    dto.setDefaultValue(ruleParam.getDefaultValue());
    dto.setDescription(ruleParam.getDescription());
    dto.setEnableConfig(ruleParam.getEnableConfig());
    dto.setRuleParamId(ruleParam.getId()); // 规则参数关联ID
    dto.setUsed(true); // 标记为已使用
    return dto;
}

/**
 * 转换共享参数实体为DTO
 */
private AuditParamDto convertToAuditParamDto(RcParamEntity sharedParam) {
    AuditParamDto dto = new AuditParamDto();
    dto.setParamId(sharedParam.getId());
    dto.setParamName(sharedParam.getParamName());
    dto.setParamType(sharedParam.getParamType());
    dto.setDefaultValue(sharedParam.getDefaultValue());
    dto.setDescription(sharedParam.getDescription());
    dto.setEnableConfig(sharedParam.getEnableConfig());
    dto.setRuleParamId(null); // 共享参数没有规则参数关联ID
    dto.setUsed(false); // 标记为未使用
    return dto;
}
```

## 🎯 业务价值

### 1. **解决新参数可用性问题**
- ✅ 新增的共享参数立即对所有规则可见
- ✅ 用户可以在规则编辑时看到所有可用参数
- ✅ 避免了参数不可用导致的规则编辑困难

### 2. **提升用户体验**
- ✅ 清晰区分已使用和未使用的参数
- ✅ 已使用参数排在前面，便于查看
- ✅ 提供完整的参数信息供用户选择

### 3. **增强系统灵活性**
- ✅ 支持动态参数扩展
- ✅ 向后兼容现有规则
- ✅ 便于规则表达式的维护和更新

## 📊 返回数据格式

### 优化前
```json
{
  "ruleId": 123,
  "ruleName": "用户年龄检查",
  "expression": "age < 18",
  "paramList": [
    {
      "paramId": 1,
      "paramName": "age",
      "paramType": "number",
      "defaultValue": "0",
      "used": true
    }
  ]
}
```

### 优化后
```json
{
  "ruleId": 123,
  "ruleName": "用户年龄检查",
  "expression": "age < 18",
  "paramList": [
    {
      "paramId": 1,
      "paramName": "age",
      "paramType": "number",
      "defaultValue": "0",
      "used": true,
      "ruleParamId": 101
    },
    {
      "paramId": 2,
      "paramName": "income",
      "paramType": "number",
      "defaultValue": "0",
      "used": false,
      "ruleParamId": null
    },
    {
      "paramId": 3,
      "paramName": "creditScore",
      "paramType": "number",
      "defaultValue": "600",
      "used": false,
      "ruleParamId": null
    }
  ]
}
```

## 🔄 使用场景

### 1. **规则编辑场景**
```javascript
// 前端可以根据 used 字段区分显示
const usedParams = paramList.filter(p => p.used);
const availableParams = paramList.filter(p => !p.used);

// 在表达式编辑器中提供所有可用参数的智能提示
const allAvailableParams = paramList.map(p => p.paramName);
```

### 2. **参数管理场景**
```javascript
// 显示参数使用状态
paramList.forEach(param => {
  if (param.used) {
    console.log(`参数 ${param.paramName} 已被规则使用`);
  } else {
    console.log(`参数 ${param.paramName} 可用于规则表达式`);
  }
});
```

### 3. **规则验证场景**
```java
// 验证规则表达式中使用的参数是否都可用
public boolean validateRuleExpression(String expression, List<AuditParamDto> paramList) {
    Set<String> availableParams = paramList.stream()
        .map(AuditParamDto::getParamName)
        .collect(Collectors.toSet());
    
    // 解析表达式中使用的参数
    Set<String> usedParams = parseParametersFromExpression(expression);
    
    // 检查所有使用的参数是否都在可用参数列表中
    return availableParams.containsAll(usedParams);
}
```

## 🎯 最佳实践

### 1. **参数命名规范**
- 使用有意义的参数名称
- 保持参数名称的一致性
- 避免参数名称冲突

### 2. **参数类型管理**
- 明确定义参数类型
- 提供合理的默认值
- 添加详细的参数描述

### 3. **性能优化**
- 考虑缓存共享参数列表
- 优化参数查询性能
- 减少不必要的数据库查询

## 🔧 扩展建议

### 1. **参数分组**
- 可以考虑为参数添加分组功能
- 按业务领域对参数进行分类
- 提供更好的参数管理界面

### 2. **参数版本管理**
- 支持参数的版本控制
- 跟踪参数的变更历史
- 提供参数回滚功能

### 3. **参数依赖管理**
- 支持参数间的依赖关系
- 提供参数依赖检查
- 避免删除被依赖的参数

---

**优化完成！** 🎉

现在规则详情查询会返回完整的参数列表，包括已使用的参数和共享参数池中未使用的参数，解决了新增参数无法在现有规则中使用的问题。
