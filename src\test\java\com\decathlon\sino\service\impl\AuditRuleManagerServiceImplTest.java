package com.decathlon.sino.service.impl;

import com.decathlon.sino.data.dao.*;
import com.decathlon.sino.data.entity.RcBizScenceEntity;
import com.decathlon.sino.data.entity.RcBlackListRuleEntity;
import com.decathlon.sino.model.input.AuditSceneInput;
import com.decathlon.sino.model.input.RuleInput;
import com.decathlon.sino.model.ouput.AuditSceneOutput;
import com.decathlon.sino.service.AuditRuleManagerService;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuditRuleManagerServiceImplTest {

    @Mock
    private RcBlackListRuleEntityRepository rcBlackListRuleEntityRepository;
    
    @Mock
    private RcBlackListRuleParamEntityRepository rcBlackListRuleParamEntityRepository;
    
    @Mock
    private RcBlackListReasonHitEntityRepository rcBlackListReasonHitEntityRepository;
    
    @Mock
    private RcBizScenceEntityRepository rcBizTypeEntityRepository;
    
    @Mock
    private RcScenceRuleEntityRepository rcScenceRuleEntityRepository;
    
    @Mock
    private RcBlackListEntityRepository rcBlackListEntityRepository;
    
    
    @Mock
    private JPAQueryFactory queryFactory;

    private AuditRuleManagerService auditRuleManagerService;

    @BeforeEach
    void setUp() {
        auditRuleManagerService = new AuditRuleManagerServiceImpl(
                rcBlackListRuleEntityRepository,
                rcBlackListRuleParamEntityRepository,
                rcBlackListEntityRepository,
                rcBizTypeEntityRepository,
                rcScenceRuleEntityRepository,
                queryFactory
        );
    }

    @Test
    void testCreateAuditScene() {
        // Given
        AuditSceneInput input = new AuditSceneInput();
        input.setClassification("TEST");
        input.setSceneName("测试场景");
        input.setSceneDesc("测试场景描述");
        input.setInterceptStatus(true);
        input.setEnableStatus(true);

        RcBizScenceEntity savedEntity = RcBizScenceEntity.builder()
                .id(1L)
                .classification("TEST")
                .sceneName("测试场景")
                .description("测试场景描述")
                .intercept(true)
                .status(true)
                .createTime(new Date())
                .updateTime(new Date())
                .build();

        when(rcBizTypeEntityRepository.save(any(RcBizScenceEntity.class))).thenReturn(savedEntity);

        // When
        AuditSceneOutput result = auditRuleManagerService.createAuditScene(input);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getSceneId());
        assertEquals("TEST", result.getClassification());
        assertEquals("测试场景", result.getSceneName());
        assertEquals("测试场景描述", result.getSceneDesc());
        assertTrue(result.getInterceptStatus());
        assertTrue(result.getEnableStatus());

        verify(rcBizTypeEntityRepository, times(1)).save(any(RcBizScenceEntity.class));
    }

    @Test
    void testCreateRule() {
        // Given
        RuleInput input = new RuleInput();
        input.setRuleName("测试规则");
        input.setRuleDesc("测试规则描述");
        input.setTriggerType("POINT");
        input.setExpression("pointBalance > 1000");
        input.setStatus(true);
        input.setCreateBy("test_user");

        RcBlackListRuleEntity savedEntity = RcBlackListRuleEntity.builder()
                .id(1L)
                .ruleName("测试规则")
                .description("测试规则描述")
                .triggerType("POINT")
                .expression("pointBalance > 1000")
                .status(true)
                .createBy("test_user")
                .createTime(new Date())
                .updateTime(new Date())
                .build();

        when(rcBlackListRuleEntityRepository.save(any(RcBlackListRuleEntity.class))).thenReturn(savedEntity);

        // When
        assertDoesNotThrow(() -> auditRuleManagerService.createRule(input));

        // Then
        verify(rcBlackListRuleEntityRepository, times(1)).save(any(RcBlackListRuleEntity.class));
    }

    @Test
    void testUpdateAuditScene() {
        // Given
        Long sceneId = 1L;
        AuditSceneInput input = new AuditSceneInput();
        input.setSceneId(sceneId);
        input.setSceneName("更新后的场景名称");
        input.setEnableStatus(false);

        RcBizScenceEntity existingEntity = RcBizScenceEntity.builder()
                .id(sceneId)
                .classification("TEST")
                .sceneName("原场景名称")
                .description("原描述")
                .intercept(true)
                .status(true)
                .createTime(new Date())
                .updateTime(new Date())
                .build();

        when(rcBizTypeEntityRepository.findById(sceneId)).thenReturn(Optional.of(existingEntity));
        when(rcBizTypeEntityRepository.save(any(RcBizScenceEntity.class))).thenReturn(existingEntity);

        // When
        assertDoesNotThrow(() -> auditRuleManagerService.updateAuditScene(input));

        // Then
        verify(rcBizTypeEntityRepository, times(1)).findById(sceneId);
        verify(rcBizTypeEntityRepository, times(1)).save(any(RcBizScenceEntity.class));
    }

    @Test
    void testUpdateAuditScene_SceneNotFound() {
        // Given
        Long sceneId = 999L;
        AuditSceneInput input = new AuditSceneInput();
        input.setSceneId(sceneId);

        when(rcBizTypeEntityRepository.findById(sceneId)).thenReturn(Optional.empty());

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> auditRuleManagerService.updateAuditScene(input)
        );

        assertEquals("场景不存在: " + sceneId, exception.getMessage());
        verify(rcBizTypeEntityRepository, times(1)).findById(sceneId);
        verify(rcBizTypeEntityRepository, never()).save(any(RcBizScenceEntity.class));
    }
}
