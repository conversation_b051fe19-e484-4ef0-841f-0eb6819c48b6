package com.decathlon.sino.service;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;

@Component
@AllArgsConstructor
public class AuditBizFactory {
	
	private final  ApplicationContext applicationContext;

	public AuditService getAuditService(Long bizType) {
		String auditServiceName = getAuditServiceName(bizType);
		if(StringUtils.isBlank(auditServiceName)) {
			throw new IllegalArgumentException("No audit service found for bizType: " + bizType );
		}
		return (AuditService) applicationContext.getBean(auditServiceName);
	}

	/**
	 * 根据业务类型和对象类型获取对应的审计服务名称
	 * @param bizType 业务类型
	 * @param objectType 对象类型
	 * @return 审计服务名称
	 */
	private String getAuditServiceName(Long bizType) {
		return bizType + "AuditService" ;
	}

}
