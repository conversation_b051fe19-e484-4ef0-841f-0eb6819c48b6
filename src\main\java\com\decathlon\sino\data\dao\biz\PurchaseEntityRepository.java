package com.decathlon.sino.data.dao.biz;

import com.decathlon.sino.data.dao.basic.QueryDslBaseDao;
import com.decathlon.sino.data.entity.biz.AccountPointEntity;
import com.decathlon.sino.data.entity.biz.PurchaseEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PurchaseEntityRepository  extends QueryDslBaseDao<PurchaseEntity> {

    PurchaseEntity findByTransactionId(String transactionId);

     @Query(nativeQuery = true, value = "SELECT * FROM biz_purchase p WHERE p.card_no = ?1 ORDER BY p.created_at DESC LIMIT ?2")
    List<PurchaseEntity> getLastByCardNumber(String cardNo, Integer limit);
}
