package com.decathlon.sino.task;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.decathlon.sino.data.dao.RcBlackListEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListReasonHitEntityRepository;

import lombok.AllArgsConstructor;

@Service("refreshDataService")
@AllArgsConstructor
public class RefreshDataService {
	
	private final RcBlackListEntityRepository rcBlackListEntityRepository;
	private final RcBlackListReasonHitEntityRepository rcBlackListReasonHitEntityRepository;
	

	@Async
	public void refreshRcBlackListReasonHit() {
		
		
	}

}
