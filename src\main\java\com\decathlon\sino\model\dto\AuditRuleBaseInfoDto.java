package com.decathlon.sino.model.dto;


import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

import java.util.Date;

@Data
@JsonNaming(SnakeCaseStrategy.class)
public class AuditRuleBaseInfoDto {
	
	private Long ruleId;
	private String ruleName;
	private String ruleDesc;
	private Integer point;
	// 启用状态
	private Boolean enableStatus;
	
	private Date freshTime;

	private Date updateTime;

	private String triggerType;

}
