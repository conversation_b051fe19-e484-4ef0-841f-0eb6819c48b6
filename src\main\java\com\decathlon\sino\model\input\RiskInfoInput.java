package com.decathlon.sino.model.input;

import java.util.List;

import com.decathlon.sino.model.bo.RiskResultDto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RiskInfoInput {
	
	private String objectId;
	private String objectType;
	private List<RiskResultDto> riskResult;

}
