package com.decathlon.sino.data.entity;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.decathlon.sino.data.entity.basic.IdEntity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_black_list_rule")
public class RcBlackListRuleEntity extends IdEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 规则名称
	 * 业务类型
	 * 对象类型
	 */
	private String ruleName;
	private Long sceneId;
	/**
	 * 表达式的触发时机
	 * 表达式
	 * 表达式描述
	 * 表达式会产生多少分
	 * 启动不启动表达式
	 */
	private String triggerType;
	
	private Integer deferMinutes;
	
	private String expression;
	private String description;
	private Boolean status;
	/**
	 * 规则创建时间
	 * 规则更新时间
	 * 创建人
	 * 更新人
	 */
	private Date createTime;
	private Date updateTime;
	private String createBy;
	private String updateBy;
	
}
