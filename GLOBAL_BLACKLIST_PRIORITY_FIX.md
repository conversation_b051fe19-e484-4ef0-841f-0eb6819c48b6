# 全局黑白名单优先级修正说明

## 🔧 问题描述

原来的风险查询逻辑存在缺陷：
- 直接查询场景级别的黑名单
- 忽略了全局黑白名单的优先级
- 可能导致全局白名单用户被误判为风险用户

## ✅ 修正方案

### 查询优先级调整

**修正前的逻辑**:
```
1. 查询场景黑名单
2. 返回结果
```

**修正后的逻辑**:
```
1. 优先查询全局黑白名单
2. 如果在全局名单中，直接返回结果
3. 如果不在全局名单中，再查询场景黑名单
4. 返回最终结果
```

## 🎯 核心改进

### 1. 直接查询模式 (checkRisk)

```java
public RiskQueryResult checkRisk(Long sceneId, String objectId) {
    log.info("直接查询用户风险: sceneId={}, objectId={}", sceneId, objectId);
    
    // 1. 优先查询全局黑白名单
    RcBlacklistGlobalEntity globalEntity = rcGlobalBlackListEntityRepository.findByObjectId(objectId);
    if (globalEntity != null) {
        log.info("用户在全局名单中: objectId={}, type={}", objectId, globalEntity.getType());
        return buildGlobalRiskResult(sceneId, objectId, globalEntity);
    }
    
    // 2. 查询场景级别的黑名单记录
    RcBlackListEntity blackListEntity = rcBlackListEntityRepository.findBySceneIdAndObjectId(sceneId, objectId);
    
    if (blackListEntity == null) {
        log.info("用户不在任何名单中: sceneId={}, objectId={}", sceneId, objectId);
        return buildNoRiskResult(sceneId, objectId);
    }
    
    // 3. 查询命中记录详情
    List<RcBlackListReasonHitEntity> hitRecords = rcBlackListReasonHitEntityRepository
            .findByBlackListId(blackListEntity.getId());
    
    // 4. 构建风险查询结果
    return buildRiskQueryResult(blackListEntity, hitRecords);
}
```

### 2. 审计评估模式 (auditRisk)

```java
public RiskEvaluationResult auditRisk(Long sceneId, String objectId, Map<String, Object> context, 
        String operator, Boolean enableRecord) {
    log.info("审计评估用户风险: sceneId={}, objectId={}, enableRecord={}", sceneId, objectId, enableRecord);
    
    // 1. 优先检查全局黑白名单
    RcBlacklistGlobalEntity globalEntity = rcGlobalBlackListEntityRepository.findByObjectId(objectId);
    if (globalEntity != null) {
        log.info("用户在全局名单中，跳过规则评估: objectId={}, type={}", objectId, globalEntity.getType());
        return buildGlobalEvaluationResult(sceneId, objectId, globalEntity);
    }
    
    // 2. 执行规则评估
    List<RuleHitResult> hitResults = riskEngineComponent.evaluateRules(sceneId, objectId, context, operator, enableRecord);
    
    // 3. 查询最新的黑名单状态
    RcBlackListEntity blackListEntity = rcBlackListEntityRepository.findBySceneIdAndObjectId(sceneId, objectId);
    
    // 4. 构建评估结果
    return buildEvaluationResult(sceneId, objectId, hitResults, blackListEntity, enableRecord);
}
```

## 🛡️ 全局名单处理逻辑

### 1. 全局黑名单处理

```java
if ("BLACKLIST".equals(globalEntity.getType())) {
    // 全局黑名单
    result.setInBlacklist(true);
    result.setRiskLevel("HIGH");
    result.setTotalScore(new BigDecimal("999")); // 全局黑名单给最高分
    result.setHitRuleCount(1);
    result.setLastHitTime(globalEntity.getCreateTime());
}
```

**特点**:
- 风险等级直接设为 `HIGH`
- 分数设为最高值 `999`
- 不需要进行规则评估
- 直接返回高风险结果

### 2. 全局白名单处理

```java
else if ("WHITELIST".equals(globalEntity.getType())) {
    // 全局白名单
    result.setInBlacklist(false);
    result.setRiskLevel("LOW");
    result.setTotalScore(BigDecimal.ZERO);
    result.setHitRuleCount(0);
    result.setLastHitTime(null);
}
```

**特点**:
- 风险等级直接设为 `LOW`
- 分数设为 `0`
- 跳过所有规则评估
- 直接返回无风险结果

### 3. 审计模式的全局名单处理

```java
private RiskEvaluationResult buildGlobalEvaluationResult(Long sceneId, String objectId, RcBlacklistGlobalEntity globalEntity) {
    RiskEvaluationResult result = new RiskEvaluationResult();
    result.setSceneId(sceneId);
    result.setObjectId(objectId);
    result.setEvaluationTime(new Date());
    result.setRecorded(false); // 全局名单不需要记录评估过程
    result.setHitRuleCount(0);
    result.setHasNewHits(false);
    result.setNewHits(Collections.emptyList());
    
    // 根据全局名单类型设置结果
    if ("BLACKLIST".equals(globalEntity.getType())) {
        result.setInBlacklist(true);
        result.setCurrentTotalScore(new BigDecimal("999"));
        result.setCurrentRiskLevel("HIGH");
    } else if ("WHITELIST".equals(globalEntity.getType())) {
        result.setInBlacklist(false);
        result.setCurrentTotalScore(BigDecimal.ZERO);
        result.setCurrentRiskLevel("LOW");
    }
    
    return result;
}
```

## 📊 业务价值

### 1. 优先级正确性
- **全局白名单**: 无论场景规则如何，都不会被误判为风险用户
- **全局黑名单**: 无论场景规则如何，都会被标记为高风险用户
- **场景规则**: 只对不在全局名单中的用户生效

### 2. 性能优化
- **全局名单用户**: 跳过复杂的规则评估过程
- **减少计算**: 全局黑名单用户不需要执行规则引擎
- **快速响应**: 全局白名单用户直接返回安全结果

### 3. 业务逻辑清晰
- **管理简单**: 全局名单统一管理特殊用户
- **优先级明确**: 全局 > 场景 > 默认
- **决策透明**: 清楚知道风险判断的依据

## 🔄 使用场景示例

### 1. VIP用户（全局白名单）
```java
// VIP用户在全局白名单中
RiskQueryResult result = auditProcessService.checkRisk(PAYMENT_SCENE_ID, "vip_user_123");

// 结果：
// inBlacklist: false
// riskLevel: "LOW"
// totalScore: 0
// 说明：即使支付金额很大，也不会触发风险规则
```

### 2. 欺诈用户（全局黑名单）
```java
// 欺诈用户在全局黑名单中
RiskEvaluationResult result = auditProcessService.auditRisk(
    LOGIN_SCENE_ID, "fraud_user_456", userContext, "system", true
);

// 结果：
// inBlacklist: true
// currentRiskLevel: "HIGH"
// currentTotalScore: 999
// hasNewHits: false
// 说明：跳过规则评估，直接返回高风险
```

### 3. 普通用户（场景规则评估）
```java
// 普通用户不在全局名单中，执行正常的场景规则评估
RiskEvaluationResult result = auditProcessService.auditRisk(
    REGISTRATION_SCENE_ID, "normal_user_789", userContext, "system", true
);

// 结果：根据场景规则的实际评估结果返回
```

## 🎯 最佳实践

### 1. 全局名单管理
- **定期审核**: 定期检查全局名单的有效性
- **权限控制**: 严格控制全局名单的修改权限
- **变更记录**: 记录全局名单的变更历史

### 2. 监控告警
- **全局名单命中率**: 监控全局名单的使用情况
- **性能提升**: 统计因全局名单跳过的规则评估次数
- **异常告警**: 全局黑名单用户的异常行为告警

### 3. 数据一致性
- **同步机制**: 确保全局名单数据的实时性
- **缓存策略**: 合理缓存全局名单数据
- **降级方案**: 全局名单查询失败时的降级策略

---

**修正完成！** 🎉

现在的风险查询服务正确实现了全局黑白名单的优先级，确保了业务逻辑的正确性和系统的可靠性。
