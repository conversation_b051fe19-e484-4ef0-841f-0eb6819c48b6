package com.decathlon.sino.task;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.decathlon.sino.common.util.RedisUtil;
import com.decathlon.sino.model.property.SchedulerProperty;

import java.util.concurrent.TimeUnit;

@Component
@ConditionalOnProperty(value = "identity.audit.enabled", havingValue = "true")
@Slf4j
@AllArgsConstructor
public class BatchExecutionScheduler {

    private final SchedulerProperty schedulerProperty;
    private final RefreshDataService refreshService;


    @Scheduled(cron = "#{schedulerProperty.refresh.cron}")
    public void refreshRcBlackListReasonHit() {
        if (Boolean.FALSE.equals(schedulerProperty.getRefresh().isEnabled())) {
            return;
        }
        if (Boolean.FALSE.equals(RedisUtil.setIfAbsent("member.platform.scheduler.refresh.lock", "1", 30, TimeUnit.MINUTES))) {
            return;
        }
        log.info("refreshRcBlackListReasonHit started");
        refreshService.refreshRcBlackListReasonHit();
        log.info("refreshRcBlackListReasonHit ended");
    }




}
