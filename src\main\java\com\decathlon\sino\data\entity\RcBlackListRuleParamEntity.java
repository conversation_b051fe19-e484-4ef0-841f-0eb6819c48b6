package com.decathlon.sino.data.entity;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.Table;
import com.decathlon.sino.data.entity.basic.IdEntity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_black_list_rule_param")
public class RcBlackListRuleParamEntity extends IdEntity {

	private static final long serialVersionUID = 1L;

	private Long ruleId;
	/**
	 * 参数名称
	 * 该参数的名称
	 * 例如：身份证号、手机号等
	 * 用于前端展示和校验
	 */
	private String paramName;
	/**
	 * 参数类型
	 * 例如：String、Integer、Date等
	 * 该参数的类型信息
	 * 用于前端展示和校验
	 */
	private String paramType;
	private String defaultValue;
	/**
	 * 参数描述
	 * 该参数的描述信息
	 * 例如：身份证号、手机号等
	 */
	private String description;
	/**
	 * 标记字段是佛能被前端修改
	 */
	private Boolean enableConfig;
	/**
	 * 是否共享
	 * 共享的参数可以被多个规则使用
	 * 在生成规则的时候会自动生成一个参数，并关联对应的规则id
	 */
	private Boolean shared;
	
	private Date createTime;
	private Date updateTime;
}
