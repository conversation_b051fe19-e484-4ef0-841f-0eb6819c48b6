package com.decathlon.sino.model.input;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BizTypeInput {

    private String classification;
    private String scene;
    private String description;
    private Boolean intercept;
    private Boolean status;
    private String createBy;
    private String updateBy;

}
