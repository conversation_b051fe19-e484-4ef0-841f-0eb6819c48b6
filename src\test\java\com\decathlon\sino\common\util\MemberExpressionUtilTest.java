package com.decathlon.sino.common.util;

import com.decathlon.sino.data.entity.biz.PurchaseEntity;
import com.decathlon.sino.kafka.biz.helper.PoslogListenerProcessHelper;
import com.decathlon.sino.model.biz.ItemInfoBpn;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.junit.jupiter.api.Test;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

import com.decathlon.sino.App;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringJUnit4ClassRunner.class)
@EnableAutoConfiguration
@SpringBootTest(classes = App.class)
@Slf4j
public class MemberExpressionUtilTest {
	@Autowired
	private PoslogListenerProcessHelper helper;

	@Test
	public void testAuditMatching() {
		helper.audit("2099132199032");
	}



//	@Test
	public void testUsualStoreInListWithSpEL() {
		
		List<String> storeList = Arrays.asList("MP&官网", "Tmall", "JD", "抖音", "PDD", "线下门店", "美团", "JDDJ");
		String store = "Tmalls";

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("store", store);
		context.setVariable("storeList", storeList);

		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#storeList.contains(#store)").getValue(context, Boolean.class);

		assertTrue(result);
	}

//	@Test
	public void testIsRecentUserWithSpEL() {
		LocalDate registerDate = LocalDate.now().minusDays(5);

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("registerDate", registerDate);

		ExpressionParser parser = new SpelExpressionParser();
		// 30天内注册
		Boolean result = parser.parseExpression("T(java.time.LocalDate).now().minusDays(30).isBefore(#registerDate)").getValue(context, Boolean.class);

		assertTrue(result);
	}

//	@Test
	public void testIsRegisterWithinDaysWithSpEL() {
		LocalDate registerDate = LocalDate.now().minusDays(10);
		int days = 30;

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("registerDate", registerDate);
		context.setVariable("days", days);

		ExpressionParser parser = new SpelExpressionParser();
		// 注册时间小于days天
		Boolean result = parser.parseExpression("T(java.time.LocalDate).now().minusDays(#days).isBefore(#registerDate)").getValue(context, Boolean.class);

		assertTrue(result);
	}

//	@Test
	public void testIsRegisterGreaterThan365DaysWithSpEL() {
		LocalDate registerDate = LocalDate.now().minusDays(400);
		int days = 365;

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("registerDate", registerDate);
		context.setVariable("days", days);

		ExpressionParser parser = new SpelExpressionParser();
		// 注册时间大于days天
		Boolean result = parser.parseExpression("#registerDate.isBefore(T(java.time.LocalDate).now().minusDays(#days))").getValue(context, Boolean.class);

		assertTrue(result);
	}

//	@Test
	public void testNameNotEmptyWithSpEL() {
		String name = "张三";
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("name", name);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#name != null && #name != ''").getValue(context, Boolean.class);
		assertTrue(result);
	}

//	@Test
	public void testGenderNotEmptyWithSpEL() {
		String gender = "男";
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("gender", gender);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#gender != null && #gender != ''").getValue(context, Boolean.class);
		assertTrue(result);
	}

	@Test
	public void testBirthdayNotEmptyWithSpEL() {
		LocalDate birthday = LocalDate.of(1990, 1, 1);
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("birthday", birthday);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#birthday != null").getValue(context, Boolean.class);
		assertTrue(result);
	}

	@Test
	public void testAllFieldsNotEmptyWithSpEL() {
		String name = "张三";
		String gender = "男";
		LocalDate birthday = LocalDate.of(1990, 1, 1);

		StandardEvaluationContext context = new StandardEvaluationContext();
//		context.setVariable("name", name);
		context.setVariable("gender", gender);
		context.setVariable("birthday", birthday);
		
		context.setVariable("other", "otherValue"); // 添加一个额外的变量以测试上下文

		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression(
			"#name != null && #name != '' && #gender != null && #gender != '' && #birthday != null"
		).getValue(context, Boolean.class);

		assertTrue(result);
	}

    @Test
    public void testPurchaseDate(){
        List<PurchaseEntity> list = new ArrayList<>();
        PurchaseEntity a = new PurchaseEntity();
        a.setPurchaseDate(LocalDateTime.now());
        list.add(a);
        PurchaseEntity b = new PurchaseEntity();
        b.setPurchaseDate(LocalDateTime.of(2025,1,1,0,0));
        list.add(b);

        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariable("days", 3);
        context.setVariable("sys_orders", list);

        ExpressionParser parser = new SpelExpressionParser();
        // 获取满足条件的订单列表
//        List<PurchaseEntity> result = parser.parseExpression(
//                "#orders.?[purchaseDate.isAfter(T(java.time.LocalDateTime).now().minusDays(#days))]"
//        ).getValue(context, List.class);
		List<PurchaseEntity> result = parser.parseExpression(
                "#sys_orders.?[T(java.time.LocalDateTime).now().minusDays(#days) <= purchaseDate]"
        ).getValue(context, List.class);

//        assertNotNull(result);
//        assertEquals(2, result.size()); // 应该有两个订单满足条件
        System.out.println("Result size is " + result);
    }
    @Test
    public void testPurchaseChannel(){
        List<PurchaseEntity> list = new ArrayList<>();
        PurchaseEntity a = new PurchaseEntity();
        a.setPurchaseDate(LocalDateTime.now());
        a.setChannel("aa");
		a.setSaleDetail("[{\"itemId\":\"5303922\",\"quantity\":101,\"price\":8.9,\"itemTotalPrice\":501,\"lineNumber\":\"1\",\"inBlacklist\":false}]");
		a.setSalePrice(new Double(5001));
        list.add(a);
        PurchaseEntity b = new PurchaseEntity();
        b.setPurchaseDate(LocalDateTime.of(2025,1,1,0,0));
		b.setChannel("bb");
		b.setSalePrice(new Double(500));

		list.add(b);
	  List<String> stores = new ArrayList<>();
	  stores.add("aa");
	  stores.add("bb");


	  StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariable("purchase_channel", "aa");
        context.setVariable("sys_purchase_channel", stores);
        context.setVariable("sys_orders", list);
        context.setVariable("days", 3);
        context.setVariable("sale_price", 5000);
        context.setVariable("item_quantity", 100);
        context.setVariable("item_price_amount", 500);

        ExpressionParser parser = new SpelExpressionParser();

		// 在这里注册函数
		try {
			context.registerFunction("hasSaleItemMatching",
					MemberExpressionUtilTest.class.getDeclaredMethod("hasSaleItemMatching",
							String.class, Integer.class, Double.class));
		} catch (NoSuchMethodException e) {
			fail("Failed to register function: " + e.getMessage());
		}
        // 获取满足条件的订单列表
//        List<PurchaseEntity> result = parser.parseExpression(
//                "#orders.?[purchaseDate.isAfter(T(java.time.LocalDateTime).now().minusDays(#days))]"
//        ).getValue(context, List.class);
		List<PurchaseEntity> result = parser.parseExpression(
                "#sys_orders.?[T(java.time.LocalDateTime).now().minusDays(#days) <= purchaseDate" +
						" && #sys_purchase_channel.contains(channel) " +
						" && salePrice > #sale_price " +
//						" && saleDetail.?[quantity > #item_quantity && itemTotalPrice > #item_price_amount] ]"
						" && #hasSaleItemMatching(saleDetail, #item_quantity, #item_price_amount)]"
		).getValue(context, List.class);


//        assertNotNull(result);
//        assertEquals(2, result.size()); // 应该有两个订单满足条件
        System.out.println("Result size is " + result);
    }

	// 静态方法用于检查销售详情中是否有满足条件的商品
	public static boolean hasSaleItemMatching(String saleDetailJson, Integer minQuantity, Double minTotalPrice) {
		if (saleDetailJson == null || saleDetailJson.isEmpty()) {
			return false;
		}

		try {
			com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
			List<java.util.Map<String, Object>> items = mapper.readValue(saleDetailJson,
					new com.fasterxml.jackson.core.type.TypeReference<List<java.util.Map<String, Object>>>(){});

			for (java.util.Map<String, Object> item : items) {
				// 安全地获取数值并转换类型
				Object quantityObj = item.get("quantity");
				Object totalPriceObj = item.get("itemTotalPrice");

				if (quantityObj == null || totalPriceObj == null) {
					continue;
				}

				int quantity = 0;
				double totalPrice = 0.0;

				if (quantityObj instanceof Number) {
					quantity = ((Number) quantityObj).intValue();
				} else if (quantityObj instanceof String) {
					quantity = Integer.parseInt((String) quantityObj);
				}

				if (totalPriceObj instanceof Number) {
					totalPrice = ((Number) totalPriceObj).doubleValue();
				} else if (totalPriceObj instanceof String) {
					totalPrice = Double.parseDouble((String) totalPriceObj);
				}

				if (quantity > minQuantity && totalPrice > minTotalPrice) {
					return true;
				}
			}
			return false;
		} catch (Exception e) {
			System.err.println("Error parsing JSON: " + e.getMessage());
			return false;
		}
	}

	@Test
	public void testpuchaseMatching() {

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("sys_purchase_date", 1735660800);

		context.setVariable("days", 3*864000);
		context.setVariable("sale_price", 5000);
		context.setVariable("item_quantity", 100);
		context.setVariable("item_price_amount", 500);

		ExpressionParser parser = new SpelExpressionParser();

		//todo 用户订单时间先变成00：00
		Boolean value = parser.parseExpression("T(java.lang.System).currentTimeMillis() / 1000 < #sys_purchase_date + (#days) ")
				.getValue(context, Boolean.class);

//        assertNotNull(result);
//        assertEquals(2, result.size()); // 应该有两个订单满足条件
				System.out.println("Result size is " + value);
	}

	@Test
	public void testChannelMatching() {

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("sys_purchase_date", 1735660800);

		context.setVariable("days", 3*864000);
		context.setVariable("sale_price", 5000);
		context.setVariable("item_quantity", 100);
		context.setVariable("item_price_amount", 500);

		ExpressionParser parser = new SpelExpressionParser();

		//todo 用户订单时间先变成00：00
		Boolean value = parser.parseExpression("#sys_purchase_channel.contains(#channel) ")
				.getValue(context, Boolean.class);

//        assertNotNull(result);
//        assertEquals(2, result.size()); // 应该有两个订单满足条件
				System.out.println("Result size is " + value);
	}

	@Test
	public void testPriceMatching() {

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("sys_purchase_date", 1735660800);

		context.setVariable("days", 3*864000);
		context.setVariable("sale_price", 5000);
		context.setVariable("sys_sale_price", 50000);
		context.setVariable("item_quantity", 100);
		context.setVariable("item_price_amount", 500);

		ExpressionParser parser = new SpelExpressionParser();

		//todo sys是数据库里的数据
		Boolean value = parser.parseExpression("#sys_sale_price > #sale_price")
				.getValue(context, Boolean.class);

//        assertNotNull(result);
//        assertEquals(2, result.size()); // 应该有两个订单满足条件
				System.out.println("Result size is " + value);
	}

	@Test
	public void testItemMatching() {
		List<ItemInfoBpn> salePartItems = new ArrayList<>();
		ItemInfoBpn itemInfoBpn = new ItemInfoBpn();
		itemInfoBpn.setQuantity(101);
		itemInfoBpn.setItemTotalPrice(50000.0);
		salePartItems.add(itemInfoBpn);

		StandardEvaluationContext context = new StandardEvaluationContext();

		context.setVariable("sys_item_quantity", new BigDecimal("55.2"));
		context.setVariable("item_quantity", new BigDecimal("55.1"));
		context.setVariable("sys_item_price_amount", 100);
		context.setVariable("item_price_amount", 100);
//		context.setVariable("sys_items",salePartItems);

		ExpressionParser parser = new SpelExpressionParser();

		//todo sys是数据库里的数据
		List<ItemInfoBpn>  value = parser.parseExpression("#sys_item_quantity > #item_quantity || #sys_item_price_amount > #item_price_amount")
				.getValue(context, List.class);

//        assertNotNull(result);
//        assertEquals(2, result.size()); // 应该有两个订单满足条件
		System.out.println("Result size is " + value);
	}

	@Test
	public void testBigDecimal(){
//		System.out.println(new BigDecimal("55.1")>new BigDecimal("0"));
	}

}
