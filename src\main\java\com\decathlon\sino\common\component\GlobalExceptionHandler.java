package com.decathlon.sino.common.component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestClientResponseException;

import com.decathlon.sino.common.exception.ServiceException;

import javax.servlet.http.HttpServletRequest;

@RestControllerAdvice
@Component
@Slf4j
public class GlobalExceptionHandler {

    private static final String EXCEPTION_MESSAGE = "exception message:{}";

    @ExceptionHandler({BindException.class})
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ResponseDTO<Void> handleError(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        return ResponseDTO.fail(bindingResult.getFieldError().getDefaultMessage());
    }
    
    @ExceptionHandler({IllegalArgumentException.class})
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ResponseDTO<Void> handleError(IllegalArgumentException e) {
        return ResponseDTO.fail(e.getMessage());
    }
    
    @ExceptionHandler({IllegalStateException.class})
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ResponseDTO<Void> handleError(IllegalStateException e) {
        return ResponseDTO.fail(e.getMessage());
    }


    @ExceptionHandler(ServiceException.class)
    @ResponseBody
    public ResponseEntity<?> globalServiceExceptionHandler(ServiceException exception) {
        log.error(EXCEPTION_MESSAGE, exception.getMessage());
        return ResponseEntity.status(HttpStatus.GONE).contentType(MediaType.APPLICATION_JSON).body(ResponseDTO.fail(exception.getCode(), exception.getMessage()));
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResponseEntity<?> globalExceptionHandler(HttpServletRequest request, Exception exception) {
        if (exception instanceof RestClientException) {
            RestClientResponseException restClientException = (RestClientResponseException) exception;
            log.error(EXCEPTION_MESSAGE, exception);
            return ResponseEntity.status(restClientException.getRawStatusCode()).contentType(MediaType.APPLICATION_JSON).body(restClientException.getResponseBodyAsString());
        }
        log.error("url={},query={},exception message={}", request.getRequestURI(), request.getQueryString(), exception);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).contentType(MediaType.APPLICATION_JSON).body(ResponseDTO.err("sys error"));
    }

}

