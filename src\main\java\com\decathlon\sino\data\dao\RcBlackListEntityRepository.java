package com.decathlon.sino.data.dao;

import com.decathlon.sino.data.dao.basic.QueryDslBaseDao;
import com.decathlon.sino.data.entity.RcBlackListEntity;

public interface RcBlackListEntityRepository extends QueryDslBaseDao<RcBlackListEntity> {
	
	/**
	 * Find RcBlackListEntity by objectId, objectType and bizType
	 * @param objectId
	 * @param objectType
	 * @param bizType
	 * @return RcBlackListEntity
	 */
	RcBlackListEntity findByObjectIdAndSceneId(String objectId,Long sceneId);


}
