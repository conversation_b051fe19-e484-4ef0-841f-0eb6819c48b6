# 审计处理服务优化说明

## 📋 优化概述

重新优化了 `AduitProcessService`，提供两种清晰的风险查询形态，满足不同业务场景的需求。

## 🎯 两种查询形态

### 1. 直接查询模式 (checkRisk)

**适用场景**: 快速风险状态检查，性能要求高的场景

**特点**:
- 直接查询黑名单数据库
- 响应速度快
- 不触发规则评估
- 返回当前风险状态

**方法签名**:
```java
public RiskQueryResult checkRisk(Long sceneId, String objectId)
```

**使用示例**:
```java
@RestController
public class RiskController {
    
    @Autowired
    private AduitProcessService auditProcessService;
    
    @GetMapping("/risk/check")
    public ResponseEntity<RiskQueryResult> checkUserRisk(
            @RequestParam Long sceneId,
            @RequestParam String userId) {
        
        RiskQueryResult result = auditProcessService.checkRisk(sceneId, userId);
        return ResponseEntity.ok(result);
    }
}
```

**返回结果示例**:
```json
{
  "sceneId": 1,
  "objectId": "user_12345",
  "inBlacklist": true,
  "riskLevel": "HIGH",
  "totalScore": 85.5,
  "hitRuleCount": 3,
  "lastHitTime": "2024-01-15T10:30:00",
  "queryTime": "2024-01-15T14:20:00",
  "hitRecords": [
    {
      "ruleId": 101,
      "hitTime": "2024-01-15T10:30:00",
      "point": 50.0,
      "context": "{\"age\":16,\"amount\":1000}"
    }
  ]
}
```

### 2. 审计评估模式 (auditRisk)

**适用场景**: 实时风险评估，需要记录评估过程的场景

**特点**:
- 先执行规则评估
- 可选择是否记录结果
- 返回评估过程信息
- 包含新命中和当前状态

**方法签名**:
```java
public RiskEvaluationResult auditRisk(Long sceneId, String objectId, 
                                    Map<String, Object> context, 
                                    String operator, Boolean enableRecord)
```

**使用示例**:
```java
@PostMapping("/risk/audit")
public ResponseEntity<RiskEvaluationResult> auditUserRisk(
        @RequestParam Long sceneId,
        @RequestParam String userId,
        @RequestBody Map<String, Object> userContext,
        @RequestParam(defaultValue = "api") String operator,
        @RequestParam(defaultValue = "true") Boolean enableRecord) {
    
    RiskEvaluationResult result = auditProcessService.auditRisk(
        sceneId, userId, userContext, operator, enableRecord
    );
    return ResponseEntity.ok(result);
}
```

**返回结果示例**:
```json
{
  "sceneId": 1,
  "objectId": "user_12345",
  "evaluationTime": "2024-01-15T14:25:00",
  "recorded": true,
  "hitRuleCount": 2,
  "hasNewHits": true,
  "newHits": [
    {
      "ruleId": 102,
      "ruleExpression": "age < 18 && amount > 500",
      "hitTime": "2024-01-15T14:25:00",
      "point": 30.0,
      "reason": "规则命中: age < 18 && amount > 500, 参数: age=16, amount=800",
      "context": "{\"age\":16,\"amount\":800}"
    }
  ],
  "inBlacklist": true,
  "currentTotalScore": 115.5,
  "currentRiskLevel": "HIGH"
}
```

## 🔧 核心功能特性

### 1. 智能风险等级计算
```java
private String calculateRiskLevel(BigDecimal score) {
    if (score == null || score.compareTo(BigDecimal.ZERO) <= 0) {
        return "LOW";        // 0分以下
    } else if (score.compareTo(new BigDecimal("50")) < 0) {
        return "MEDIUM";     // 0-50分
    } else {
        return "HIGH";       // 50分以上
    }
}
```

### 2. 完整的数据聚合
- **黑名单状态**: 是否在黑名单中
- **风险分数**: 累计风险分数
- **命中历史**: 详细的规则命中记录
- **时间信息**: 最后命中时间、查询时间等

### 3. 灵活的记录控制
- **enableRecord=true**: 记录评估结果到数据库
- **enableRecord=false**: 仅评估不记录，用于测试或预览

## 📊 使用场景对比

| 场景 | 直接查询模式 | 审计评估模式 |
|------|-------------|-------------|
| **登录验证** | ✅ 快速检查用户状态 | ❌ 性能开销大 |
| **订单支付** | ❌ 需要实时评估 | ✅ 实时风险评估 |
| **批量检查** | ✅ 高性能批量查询 | ❌ 不适合批量 |
| **风险监控** | ✅ 定期状态检查 | ❌ 无需重复评估 |
| **实时拦截** | ❌ 可能数据滞后 | ✅ 最新风险状态 |
| **数据分析** | ✅ 历史数据分析 | ❌ 重复评估无意义 |

## 🚀 性能优化建议

### 1. 直接查询模式优化
```java
// 缓存热点用户的风险状态
@Cacheable(value = "userRisk", key = "#sceneId + '_' + #objectId")
public RiskQueryResult checkRisk(Long sceneId, String objectId) {
    // 实现逻辑
}

// 批量查询优化
public Map<String, RiskQueryResult> batchCheckRisk(Long sceneId, List<String> objectIds) {
    List<RcBlackListEntity> blacklists = rcBlackListEntityRepository
        .findBySceneIdAndObjectIdIn(sceneId, objectIds);
    // 批量处理逻辑
}
```

### 2. 审计评估模式优化
```java
// 异步记录评估结果
@Async
public CompletableFuture<Void> auditRiskAsync(Long sceneId, String objectId, 
                                            Map<String, Object> context, String operator) {
    auditRisk(sceneId, objectId, context, operator, true);
    return CompletableFuture.completedFuture(null);
}
```

## 🛡️ 错误处理和监控

### 1. 异常处理
```java
try {
    RiskQueryResult result = auditProcessService.checkRisk(sceneId, userId);
    return ResponseEntity.ok(result);
} catch (Exception e) {
    log.error("风险查询失败: sceneId={}, userId={}", sceneId, userId, e);
    return ResponseEntity.status(500).body("风险查询服务异常");
}
```

### 2. 监控指标
- **查询响应时间**: 直接查询 vs 审计评估的性能对比
- **命中率统计**: 各场景的风险命中率
- **评估频率**: 审计评估的调用频率和成功率

### 3. 日志记录
```java
// 关键操作日志
log.info("直接查询用户风险: sceneId={}, objectId={}, result={}", 
         sceneId, objectId, result.getInBlacklist());

log.info("审计评估用户风险: sceneId={}, objectId={}, hitCount={}, recorded={}", 
         sceneId, objectId, result.getHitRuleCount(), result.getRecorded());
```

## 🔄 集成示例

### 1. 用户登录场景
```java
@Service
public class LoginService {
    
    @Autowired
    private AduitProcessService auditProcessService;
    
    public LoginResult login(String username, String password) {
        // 1. 验证用户名密码
        User user = validateCredentials(username, password);
        
        // 2. 快速风险检查
        RiskQueryResult riskResult = auditProcessService.checkRisk(LOGIN_SCENE_ID, user.getId());
        
        if (riskResult.getInBlacklist() && "HIGH".equals(riskResult.getRiskLevel())) {
            throw new SecurityException("用户存在高风险，禁止登录");
        }
        
        return LoginResult.success(user);
    }
}
```

### 2. 订单支付场景
```java
@Service
public class PaymentService {
    
    @Autowired
    private AduitProcessService auditProcessService;
    
    public PaymentResult processPayment(PaymentRequest request) {
        // 1. 构建风险评估上下文
        Map<String, Object> context = buildPaymentContext(request);
        
        // 2. 实时风险评估
        RiskEvaluationResult riskResult = auditProcessService.auditRisk(
            PAYMENT_SCENE_ID, request.getUserId(), context, "payment", true
        );
        
        // 3. 根据评估结果决定是否允许支付
        if (riskResult.getHasNewHits() && "HIGH".equals(riskResult.getCurrentRiskLevel())) {
            return PaymentResult.rejected("支付风险过高，请联系客服");
        }
        
        return processPaymentInternal(request);
    }
}
```

---

**优化完成！** 🎉

新的审计处理服务提供了清晰的两种查询形态，满足不同业务场景的风险查询需求。
