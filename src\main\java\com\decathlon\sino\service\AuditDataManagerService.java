package com.decathlon.sino.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.decathlon.sino.common.config.PageResultDTO;
import com.decathlon.sino.model.bo.ImportRow;
import com.decathlon.sino.model.criteria.ListDataSearchCriteria;
import com.decathlon.sino.model.ouput.RiskInfoOutput;

public interface AuditDataManagerService {

	String importRiskList(List<ImportRow>  importListDto,String bizType,String operator,Boolean status);
	
	String importRiskList(List<ImportRow>  importListDto,String bizType,String objectType, String operator,Boolean status);
		
	List<ImportRow> refineImportData(MultipartFile file);

	PageResultDTO<ImportRow> queryRisks(ListDataSearchCriteria search);

	List<RiskInfoOutput> riskStatics(String bizType);

	ImportRow refineData(ImportRow importRow);

	String deleteRiskList(Long personId, String bizType, String operator, Boolean status);

}
