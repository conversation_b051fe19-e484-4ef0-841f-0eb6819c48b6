package com.decathlon.sino.model.ouput;

import java.util.Date;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RuleHitRecordOutput {
	
	private String objectId;
	private Date createTime;
	private String sceneName;

	private String ruleName;
	private String ruleDesc;

}
