package com.decathlon.sino.service.impl;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.decathlon.sino.component.RiskEngineComponent;
import com.decathlon.sino.data.dao.RcBizScenceEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListReasonHitEntityRepository;
import com.decathlon.sino.data.dao.RcBlacklistGlobalEntityRepository;
import com.decathlon.sino.data.entity.RcBlackListEntity;
import com.decathlon.sino.data.entity.RcBlackListReasonHitEntity;
import com.decathlon.sino.data.entity.RcBlacklistGlobalEntity;
import com.decathlon.sino.data.entity.RcBizScenceEntity;
import com.decathlon.sino.model.output.RiskQueryResult;
import com.decathlon.sino.model.bo.RuleHitResult;
import com.decathlon.sino.model.output.RiskEvaluationResult;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 审计处理服务 - 用户风险查询接口
 *
 * 提供两种风险查询形态：
 * 1. 直接查询：通过场景ID和objectId直接查询黑名单数据
 * 2. 审计查询：先进行规则评估，再返回结果
 */
@Service
@AllArgsConstructor
@Slf4j
public class AduitProcessService {

	private final RiskEngineComponent riskEngineComponent;
	private final RcBlackListEntityRepository rcBlackListEntityRepository;
	private final RcBlackListReasonHitEntityRepository rcBlackListReasonHitEntityRepository;
	private final RcBizScenceEntityRepository rcBizScenceEntityRepository;
	private final RcBlacklistGlobalEntityRepository rcGlobalBlackListEntityRepository;

	/**
	 * 直接查询模式：按优先级查询黑名单数据
	 *
	 * 查询优先级：
	 * 1. 全局黑白名单
	 * 2. 全局场景黑名单
	 * 3. 场景黑名单
	 *
	 * @param sceneId 场景ID
	 * @param objectId 对象ID
	 * @return 风险查询结果
	 */
	public RiskQueryResult checkRisk(Long sceneId, String objectId) {
		log.info("直接查询用户风险: sceneId={}, objectId={}", sceneId, objectId);

		// 1. 优先查询全局黑白名单
		RcBlacklistGlobalEntity globalEntity = rcGlobalBlackListEntityRepository.findByBlacklistValueAndSceneId(objectId,sceneId);
		if (globalEntity != null) {
			log.info("用户在全局名单中: objectId={}, type={}", objectId, globalEntity.getScene());
			return buildGlobalRiskResult(sceneId, objectId, globalEntity);
		}

		// 2. 查询全局场景黑名单
		RcBlacklistGlobalEntity globalSceneEntity = rcGlobalBlackListEntityRepository.findByBlacklistValueAndSceneId(objectId,sceneId);
		if (globalSceneEntity != null) {
			log.info("用户在全局场景黑名单中: sceneId={}, objectId={}", sceneId, objectId);
			return buildGlobalRiskResult(sceneId, objectId, globalEntity);
		}

		// 3. 查询场景级别的黑名单记录
		RcBlackListEntity blackListEntity = rcBlackListEntityRepository.findByObjectIdAndSceneId(objectId,sceneId);

		if (blackListEntity == null) {
			log.info("用户不在任何名单中: sceneId={}, objectId={}", sceneId, objectId);
			return buildNoRiskResult(sceneId, objectId);
		}

		// 4. 查询命中记录详情
		List<RcBlackListReasonHitEntity> hitRecords = rcBlackListReasonHitEntityRepository.findByBlackListId(blackListEntity.getId());

		// 5. 查询场景拦截状态
		RcBizScenceEntity sceneEntity = rcBizScenceEntityRepository.findById(sceneId).orElse(null);

		// 6. 构建风险查询结果
		return buildRiskQueryResult(blackListEntity, hitRecords, sceneEntity);
	}

	/**
	 * 审计评估模式：按优先级检查后进行规则评估
	 *
	 * 查询优先级：
	 * 1. 全局黑白名单
	 * 2. 全局场景黑名单
	 * 3. 规则评估 + 场景黑名单
	 *
	 * @param sceneId 场景ID
	 * @param objectId 对象ID
	 * @param context 上下文参数
	 * @param operator 操作人
	 * @param enableRecord 是否记录评估结果
	 * @return 风险评估结果
	 */
	public RiskEvaluationResult auditRisk(Long sceneId, String objectId, Map<String, Object> context,
			String operator, Boolean enableRecord) {
		log.info("审计评估用户风险: sceneId={}, objectId={}, enableRecord={}", sceneId, objectId, enableRecord);

		// 1. 优先检查全局黑白名单
		RcBlacklistGlobalEntity globalEntity = rcGlobalBlackListEntityRepository.findByBlacklistValueAndSceneId(objectId,sceneId);
		if (globalEntity != null) {
			log.info("用户在全局名单中，跳过规则评估: objectId={}, type={}", objectId, globalEntity.getScene());
			return buildGlobalEvaluationResult(sceneId, objectId, globalEntity);
		}

		// 2. 检查全局场景黑名单
		RcBlacklistGlobalEntity globalSceneEntity = rcGlobalBlackListEntityRepository.findByBlacklistValueAndSceneId(objectId,sceneId);
		if (globalSceneEntity != null) {
			log.info("用户在全局场景黑名单中，跳过规则评估: sceneId={}, objectId={}", sceneId, objectId);
			return buildGlobalEvaluationResult(sceneId, objectId, globalEntity);
		}

		// 3. 执行规则评估
		List<RuleHitResult> hitResults = riskEngineComponent.evaluateRules(sceneId, objectId, context, operator, enableRecord);

		// 4. 查询最新的黑名单状态
		RcBlackListEntity blackListEntity = rcBlackListEntityRepository.findByObjectIdAndSceneId(objectId,sceneId);

		// 5. 查询场景拦截状态
		RcBizScenceEntity sceneEntity = rcBizScenceEntityRepository.findById(sceneId).orElse(null);

		// 6. 构建评估结果
		return buildEvaluationResult(sceneId, objectId, hitResults, blackListEntity, sceneEntity, enableRecord);
	}

	/**
	 * 构建全局名单风险结果
	 */
	private RiskQueryResult buildGlobalRiskResult(Long sceneId, String objectId, RcBlacklistGlobalEntity globalEntity) {
		RiskQueryResult result = new RiskQueryResult();
		result.setSceneId(sceneId);
		result.setObjectId(objectId);
		result.setQueryTime(new Date());
		result.setHitRecords(Collections.emptyList());
		result.setBlocked(true); // 全局名单默认拦截

		if (Boolean.TRUE.equals(globalEntity.getStatus())) {
			// 全局黑名单
			result.setInBlacklist(true);
			result.setRiskLevel("HIGH");
			result.setTotalScore(new BigDecimal("999")); // 全局黑名单给最高分
			result.setHitRuleCount(1);
			result.setLastHitTime(globalEntity.getCreatedTime());
		} else if (Boolean.FALSE.equals(globalEntity.getStatus())) {
			// 全局白名单
			result.setInBlacklist(false);
			result.setRiskLevel("LOW");
			result.setTotalScore(BigDecimal.ZERO);
			result.setHitRuleCount(0);
			result.setLastHitTime(null);
			result.setBlocked(false); // 全局白名单不拦截
		} else {
			// 未知类型，按无风险处理
			result.setInBlacklist(false);
			result.setRiskLevel("LOW");
			result.setTotalScore(BigDecimal.ZERO);
			result.setHitRuleCount(0);
			result.setLastHitTime(null);
		}

		return result;
	}

	/**
	 * 构建全局场景黑名单风险结果
	 */
	public RiskQueryResult buildGlobalSceneRiskResult(Long sceneId, String objectId, RcBlacklistGlobalEntity globalSceneEntity) {
		RiskQueryResult result = new RiskQueryResult();
		result.setSceneId(sceneId);
		result.setObjectId(objectId);
		result.setInBlacklist(true);
		result.setRiskLevel("HIGH");
		result.setTotalScore(new BigDecimal("888")); // 全局场景黑名单给高分
		result.setHitRuleCount(1);
		result.setLastHitTime(globalSceneEntity.getCreatedTime());
		result.setQueryTime(new Date());
		result.setHitRecords(Collections.emptyList());
		result.setBlocked(true); // 全局场景黑名单默认拦截

		return result;
	}

	/**
	 * 构建全局名单评估结果
	 */
	private RiskEvaluationResult buildGlobalEvaluationResult(Long sceneId, String objectId, RcBlacklistGlobalEntity globalEntity) {
		RiskEvaluationResult result = new RiskEvaluationResult();
		result.setSceneId(sceneId);
		result.setObjectId(objectId);
		result.setEvaluationTime(new Date());
		result.setRecorded(false); // 全局名单不需要记录评估过程
		result.setHitRuleCount(0);
		result.setHasNewHits(false);
		result.setNewHits(Collections.emptyList());
		result.setBlocked(true); // 全局名单默认拦截

		if (Boolean.TRUE.equals(globalEntity.getStatus())) {
			// 全局黑名单
			result.setInBlacklist(true);
			result.setCurrentTotalScore(new BigDecimal("999"));
			result.setCurrentRiskLevel("HIGH");
		} else if (Boolean.FALSE.equals(globalEntity.getStatus())) {
			// 全局白名单
			result.setInBlacklist(false);
			result.setCurrentTotalScore(BigDecimal.ZERO);
			result.setCurrentRiskLevel("LOW");
			result.setBlocked(false); // 全局白名单不拦截
		} else {
			// 未知类型，按无风险处理
			result.setInBlacklist(false);
			result.setCurrentTotalScore(BigDecimal.ZERO);
			result.setCurrentRiskLevel("LOW");
		}

		return result;
	}

	/**
	 * 构建全局场景黑名单评估结果
	 */
	public RiskEvaluationResult buildGlobalSceneEvaluationResult(Long sceneId, String objectId, RcBlacklistGlobalEntity globalSceneEntity) {
		RiskEvaluationResult result = new RiskEvaluationResult();
		result.setSceneId(sceneId);
		result.setObjectId(objectId);
		result.setEvaluationTime(new Date());
		result.setRecorded(false); // 全局场景黑名单不需要记录评估过程
		result.setHitRuleCount(0);
		result.setHasNewHits(false);
		result.setNewHits(Collections.emptyList());
		result.setInBlacklist(true);
		result.setCurrentTotalScore(new BigDecimal("888"));
		result.setCurrentRiskLevel("HIGH");
		result.setBlocked(true); // 全局场景黑名单默认拦截

		return result;
	}

	/**
	 * 构建无风险结果
	 */
	private RiskQueryResult buildNoRiskResult(Long sceneId, String objectId) {
		RiskQueryResult result = new RiskQueryResult();
		result.setSceneId(sceneId);
		result.setObjectId(objectId);
		result.setInBlacklist(false);
		result.setRiskLevel("LOW");
		result.setTotalScore(BigDecimal.ZERO);
		result.setHitRuleCount(0);
		result.setLastHitTime(null);
		result.setHitRecords(Collections.emptyList());
		result.setQueryTime(new Date());
		result.setBlocked(false); // 无风险不拦截
		return result;
	}

	/**
	 * 构建风险查询结果
	 */
	private RiskQueryResult buildRiskQueryResult(RcBlackListEntity blackListEntity, List<RcBlackListReasonHitEntity> hitRecords, RcBizScenceEntity sceneEntity) {
		RiskQueryResult result = new RiskQueryResult();
		result.setSceneId(blackListEntity.getSceneId());
		result.setObjectId(blackListEntity.getObjectId());
		result.setInBlacklist(true);
		result.setTotalScore(blackListEntity.getFinalScore());
		result.setHitRuleCount(hitRecords.size());
		result.setQueryTime(new Date());

		// 设置风险等级
		result.setRiskLevel(calculateRiskLevel(blackListEntity.getFinalScore()));

		// 设置最后命中时间
		Date lastHitTime = hitRecords.stream()
				.map(RcBlackListReasonHitEntity::getCreateTime)
				.max(Date::compareTo)
				.orElse(null);
		result.setLastHitTime(lastHitTime);

		// 转换命中记录
		List<RiskQueryResult.HitRecord> hitRecordList = hitRecords.stream()
				.map(this::convertToHitRecord)
				.toList();
		result.setHitRecords(hitRecordList);

		// 设置拦截状态（基于场景配置）
		if (sceneEntity != null && sceneEntity.getIntercept() != null) {
			result.setBlocked(sceneEntity.getIntercept());
		} else {
			result.setBlocked(false); // 默认不拦截
		}

		return result;
	}

	/**
	 * 构建评估结果
	 */
	private RiskEvaluationResult buildEvaluationResult(Long sceneId, String objectId, List<RuleHitResult> hitResults,
			RcBlackListEntity blackListEntity, RcBizScenceEntity sceneEntity, Boolean enableRecord) {
		RiskEvaluationResult result = new RiskEvaluationResult();
		result.setSceneId(sceneId);
		result.setObjectId(objectId);
		result.setEvaluationTime(new Date());
		result.setRecorded(enableRecord);

		// 设置评估结果
		result.setHitRuleCount(hitResults.size());
		result.setHasNewHits(!hitResults.isEmpty());

		// 转换命中结果
		List<RiskEvaluationResult.EvaluationHit> evaluationHits = hitResults.stream()
				.map(this::convertToEvaluationHit)
				.toList();
		result.setNewHits(evaluationHits);

		// 设置当前黑名单状态
		if (blackListEntity != null) {
			result.setInBlacklist(true);
			result.setCurrentTotalScore(blackListEntity.getFinalScore());
			result.setCurrentRiskLevel(calculateRiskLevel(blackListEntity.getFinalScore()));
		} else {
			result.setInBlacklist(false);
			result.setCurrentTotalScore(BigDecimal.ZERO);
			result.setCurrentRiskLevel("LOW");
		}

		// 设置拦截状态（基于场景配置和风险状态）
		if (sceneEntity != null && sceneEntity.getIntercept() != null) {
			// 如果场景配置了拦截，且用户在黑名单中或有新命中，则拦截
			result.setBlocked(sceneEntity.getIntercept() && (result.getInBlacklist() || result.getHasNewHits()));
		} else {
			result.setBlocked(false); // 默认不拦截
		}

		return result;
	}

	/**
	 * 计算风险等级
	 */
	private String calculateRiskLevel(BigDecimal score) {
		if (score == null || score.compareTo(BigDecimal.ZERO) <= 0) {
			return "LOW";
		} else if (score.compareTo(new BigDecimal("50")) < 0) {
			return "MEDIUM";
		} else {
			return "HIGH";
		}
	}

	/**
	 * 转换命中记录
	 */
	private RiskQueryResult.HitRecord convertToHitRecord(RcBlackListReasonHitEntity entity) {
		RiskQueryResult.HitRecord hitRecord = new RiskQueryResult.HitRecord();
		hitRecord.setRuleId(entity.getRuleId());
		hitRecord.setHitTime(entity.getCreateTime());
		hitRecord.setPoint(entity.getPoint());
		hitRecord.setContext(entity.getContext());
		return hitRecord;
	}

	/**
	 * 转换评估命中结果
	 */
	private RiskEvaluationResult.EvaluationHit convertToEvaluationHit(RuleHitResult hitResult) {
		RiskEvaluationResult.EvaluationHit hit = new RiskEvaluationResult.EvaluationHit();
		hit.setRuleId(hitResult.getRuleId());
		hit.setRuleExpression(hitResult.getRuleExpression());
		hit.setHitTime(hitResult.getHitTime());
		hit.setPoint(hitResult.getPoint());
		hit.setReason(hitResult.getReason());
		hit.setContext(hitResult.getContext());
		return hit;
	}
	

}
