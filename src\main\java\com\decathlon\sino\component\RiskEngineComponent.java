package com.decathlon.sino.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.decathlon.sino.common.util.ExpressionUtil;
import com.decathlon.sino.data.dao.RcBlackListEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListReasonHitEntityRepository;
import com.decathlon.sino.data.entity.RcBlackListEntity;
import com.decathlon.sino.data.entity.RcBlackListReasonHitEntity;
import com.decathlon.sino.data.entity.RcBlackListRuleEntity;
import com.decathlon.sino.model.bo.ParamDef;
import com.decathlon.sino.model.bo.RuleDefinition;
import com.decathlon.sino.model.bo.RuleHitResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 风险引擎组件 - 规则命中检测和记录
 *
 * 主要功能：
 * 1. 通过规则缓存获取场景相关的所有规则
 * 2. 预处理规则参数，准备表达式执行环境
 * 3. 执行规则表达式，检测是否命中
 * 4. 命中时记录黑名单和命中详情
 * 5. 提供详细的命中原因和上下文信息
 *
 * 业务流程：
 * - 输入：场景ID、对象ID、上下文参数
 * - 输出：规则命中结果列表
 * - 副作用：在数据库中记录命中信息
 */
@Component
@AllArgsConstructor
@Slf4j
public class RiskEngineComponent {

	private final RuleCacheComponent ruleCacheComponent;
	private final RcBlackListReasonHitEntityRepository rcBlackListReasonHitEntityRepository;
	private final RcBlackListEntityRepository rcBlackListEntityRepository;
	private final ObjectMapper objectMapper = new ObjectMapper();

	/**
	 * 评估风险规则命中情况
	 *
	 * @param sceneId 场景ID
	 * @param objectId 对象ID（如用户ID、订单ID等）
	 * @param context 上下文参数
	 * @param operator 操作人
	 * @param enableRecord 是否启用记录（true=记录到数据库，false=仅检测）
	 * @return 规则命中结果列表
	 */
	@Transactional
	public List<RuleHitResult> evaluateRules(Long sceneId, String objectId, Map<String, Object> context, String operator, Boolean enableRecord) {
		log.info("开始评估风险规则: sceneId={}, objectId={}, enableRecord={}", sceneId, objectId, enableRecord);

		List<RuleHitResult> hitResults = new ArrayList<>();

		// 1. 获取场景相关的所有规则
		List<RuleDefinition> rules = ruleCacheComponent.getRulesBySceneId(sceneId);
		if (rules.isEmpty()) {
			log.warn("场景 {} 没有配置任何规则", sceneId);
			return hitResults;
		}

		log.debug("场景 {} 共有 {} 个规则需要评估", sceneId, rules.size());

		// 2. 逐个评估规则
		for (RuleDefinition rule : rules) {
			try {
				RuleHitResult hitResult = evaluateSingleRule(rule, objectId, context, operator, enableRecord, sceneId);
				if (hitResult != null) {
					hitResults.add(hitResult);
				}
			} catch (Exception e) {
				log.error("评估规则失败: ruleId={}, objectId={}", rule.getRuleId(), objectId, e);
				// 继续评估其他规则，不因单个规则失败而中断
			}
		}

		log.info("规则评估完成: sceneId={}, objectId={}, 总规则数={}, 命中数={}",
				sceneId, objectId, rules.size(), hitResults.size());

		return hitResults;
	}

	/**
	 * 评估单个规则
	 */
	private RuleHitResult evaluateSingleRule(RuleDefinition rule, String objectId, Map<String, Object> context, String operator, Boolean enableRecord,Long sceneId) {
		log.debug("评估规则: ruleId={}, expression={}", rule.getRuleId(), rule.getExpr());

		// 1. 预处理规则参数
		Map<String, Object> processedParams = preprocessRuleParams(rule, context);

		// 2. 检查参数完整性
		if (!validateRuleParams(rule, processedParams)) {
			log.warn("规则参数不完整，跳过评估: ruleId={}, 期望参数数={}, 实际参数数={}",
					rule.getRuleId(), rule.getParams().size(), processedParams.size());
			return null;
		}
		// 3. 构建命中结果
		RuleHitResult hitResult = buildHitResult(rule, objectId, processedParams, operator);

		// 4. 执行规则表达式
		boolean isHit = executeRuleExpression(rule, processedParams);

		if (!isHit) {
			log.debug("规则未命中: ruleId={}", rule.getRuleId());
			removeRecordRuleHit(hitResult,sceneId);
			return null;
		}

		log.info("规则命中: ruleId={}, objectId={}", rule.getRuleId(), objectId);

		
		// 5. 记录命中信息到数据库
		if (Boolean.TRUE.equals(enableRecord)) {
			recordRuleHit(hitResult,sceneId);
		}
		
		return hitResult;
	}

	/**
	 * 移除记录规则命中信息
	 */
	private void removeRecordRuleHit(RuleHitResult hitResult, Long sceneId) {
		try {
			// 1. 查找或创建黑名单记录
			Long blackListId = findOrCreateBlackList(hitResult,sceneId);

			// 2. 移除命中记录
			removeRecordHitDetails(hitResult, blackListId);

			log.info("移除命中记录成功: ruleId={}, objectId={}, blackListId={}",
					hitResult.getRuleId(), hitResult.getObjectId(), blackListId);
		} catch (Exception e) {
			log.error("移除命中记录失败: ruleId={}, objectId={}",
					hitResult.getRuleId(), hitResult.getObjectId(), e);
			throw e;
		}
	}
	
	/**
	 * 移除记录命中详情
	 * @param hitResult
	 * @param blackListId
	 */
	private void removeRecordHitDetails(RuleHitResult hitResult, Long blackListId) {
		
		RcBlackListReasonHitEntity hitEntity =rcBlackListReasonHitEntityRepository.findByBlackListIdAndRuleId(blackListId, hitResult.getRuleId());
		if(hitEntity != null) {
			Integer point = hitEntity.getPoint() != null ? hitEntity.getPoint().intValue() : 0;
			rcBlackListReasonHitEntityRepository.delete(hitEntity);
			log.info("移除命中详情成功: ruleId={}, objectId={}, blackListId={}, point={}", 
					hitResult.getRuleId(), hitResult.getObjectId(), blackListId, point);
			
			// 更新黑名单分数
			rcBlackListEntityRepository.findById(blackListId).ifPresent(blackList -> {
				BigDecimal currentScore = blackList.getFinalScore() != null ? blackList.getFinalScore() : BigDecimal.ZERO;
				blackList.setFinalScore(currentScore.subtract(BigDecimal.valueOf(point)));
				blackList.setUpdateTime(new Date());
				rcBlackListEntityRepository.save(blackList);
			});
		}

	}

	/**
	 * 预处理规则参数
	 * 将上下文参数和规则默认参数合并，为表达式执行做准备
	 */
	private Map<String, Object> preprocessRuleParams(RuleDefinition rule, Map<String, Object> context) {
		Map<String, Object> processedParams = new HashMap<>();

		for (ParamDef param : rule.getParams()) {
			String paramName = param.getName();
			Object paramValue = null;

			// 优先级：上下文参数 > 默认参数值
			if (context.containsKey(paramName)) {
				paramValue = context.get(paramName);
				log.debug("使用上下文参数: {}={}", paramName, paramValue);
			} else if (Boolean.TRUE.equals(param.getEnableConfig()) && param.getDefaultValue() != null) {
				paramValue = param.getDefaultValue();
				log.debug("使用默认参数: {}={}", paramName, paramValue);
			}

			if (paramValue != null) {
				processedParams.put(paramName, paramValue);
			}
		}

		log.debug("规则 {} 参数预处理完成: {}", rule.getRuleId(), processedParams);
		return processedParams;
	}

	/**
	 * 验证规则参数完整性
	 */
	private boolean validateRuleParams(RuleDefinition rule, Map<String, Object> processedParams) {
		// 检查必需参数是否都有值
		List<String> missingParams = rule.getParams().stream()
				.map(ParamDef::getName)
				.filter(paramName -> !processedParams.containsKey(paramName))
				.toList();

		if (!missingParams.isEmpty()) {
			log.warn("规则 {} 缺少必需参数: {}", rule.getRuleId(), missingParams);
			return false;
		}

		return true;
	}

	/**
	 * 执行规则表达式
	 */
	private boolean executeRuleExpression(RuleDefinition rule, Map<String, Object> params) {
		try {
			// 使用表达式工具执行规则
			BigDecimal result = ExpressionUtil.evaluateExpression(rule.getExpr(), params);

			// 规则命中判断：结果大于0表示命中
			boolean isHit = result != null && result.compareTo(BigDecimal.ZERO) > 0;

			log.debug("规则表达式执行结果: ruleId={}, expression={}, result={}, isHit={}",
					rule.getRuleId(), rule.getExpr(), result, isHit);

			return isHit;
		} catch (Exception e) {
			log.error("规则表达式执行失败: ruleId={}, expression={}", rule.getRuleId(), rule.getExpr(), e);
			return false;
		}
	}

	/**
	 * 构建命中结果对象
	 */
	private RuleHitResult buildHitResult(RuleDefinition rule, String objectId, Map<String, Object> params, String operator) {
		RuleHitResult hitResult = new RuleHitResult();
		hitResult.setRuleId(rule.getRuleId());
		hitResult.setObjectId(objectId);
		hitResult.setHitTime(new Date());
		hitResult.setOperator(operator);
		hitResult.setRuleExpression(rule.getExpr());
		hitResult.setPoint(rule.getPoint());

		// 序列化参数上下文
		try {
			String contextJson = objectMapper.writeValueAsString(params);
			hitResult.setContext(contextJson);
		} catch (JsonProcessingException e) {
			log.warn("序列化规则参数失败: ruleId={}", rule.getRuleId(), e);
			hitResult.setContext(params.toString());
		}

		// 生成命中原因描述
		String reason = generateHitReason(rule, params);
		hitResult.setReason(reason);

		return hitResult;
	}

	/**
	 * 生成命中原因描述
	 */
	private String generateHitReason(RuleDefinition rule, Map<String, Object> params) {
		StringBuilder reason = new StringBuilder();
		reason.append("规则命中: ").append(rule.getExpr());

		if (!params.isEmpty()) {
			reason.append(", 参数: ");
			params.forEach((key, value) ->
				reason.append(key).append("=").append(value).append(", ")
			);
			// 移除最后的逗号和空格
			if (reason.length() > 2) {
				reason.setLength(reason.length() - 2);
			}
		}

		return reason.toString();
	}

	/**
	 * 记录规则命中信息到数据库
	 */
	private void recordRuleHit(RuleHitResult hitResult,Long sceneId) {
		try {
			// 1. 查找或创建黑名单记录
			Long blackListId = findOrCreateBlackList(hitResult,sceneId);

			// 2. 记录命中详情
			recordHitDetails(hitResult, blackListId);

			log.info("规则命中记录保存成功: ruleId={}, objectId={}, blackListId={}",
					hitResult.getRuleId(), hitResult.getObjectId(), blackListId);
		} catch (Exception e) {
			log.error("记录规则命中信息失败: ruleId={}, objectId={}",
					hitResult.getRuleId(), hitResult.getObjectId(), e);
			throw e;
		}
	}

	/**
	 * 查找或创建黑名单记录
	 * 黑名单基于场景ID和对象ID的组合创建
	 */
	private Long findOrCreateBlackList(RuleHitResult hitResult, Long sceneId) {
		// 查找是否已存在黑名单记录（基于场景ID和对象ID）
		RcBlackListEntity existingBlackList = rcBlackListEntityRepository
				.findByObjectIdAndSceneId(hitResult.getObjectId(), sceneId);

		if (existingBlackList != null) {
			// 更新现有记录
			existingBlackList.setUpdateTime(new Date());
			if (hitResult.getPoint() != null) {
				// 累加分数（如果需要）
				BigDecimal currentScore = existingBlackList.getFinalScore() != null ?
						existingBlackList.getFinalScore() : BigDecimal.ZERO;
				existingBlackList.setFinalScore(currentScore.add(hitResult.getPoint()));
				existingBlackList.setUpdateTime(new Date());
				existingBlackList.setUpdateBy(hitResult.getOperator());
			}
			rcBlackListEntityRepository.save(existingBlackList);
			return existingBlackList.getId();
		} else {
			// 创建新的黑名单记录
			RcBlackListEntity newBlackList = new RcBlackListEntity();
			newBlackList.setSceneId(sceneId);  // 设置场景ID
			newBlackList.setCreateBy(hitResult.getOperator()); // 操作人
			newBlackList.setObjectId(hitResult.getObjectId());
			newBlackList.setFinalScore(hitResult.getPoint() != null ? hitResult.getPoint() : BigDecimal.ZERO);
			newBlackList.setCreateTime(new Date());

			RcBlackListEntity savedBlackList = rcBlackListEntityRepository.save(newBlackList);
			return savedBlackList.getId();
		}
	}

	/**
	 * 记录命中详情
	 */
	private void recordHitDetails(RuleHitResult hitResult, Long blackListId) {
		RcBlackListReasonHitEntity hitEntity = new RcBlackListReasonHitEntity();
		RcBlackListRuleEntity rcBlackListRuleEntity =  ruleCacheComponent.getRule(hitResult.getRuleId());
		if(rcBlackListRuleEntity == null) {
			log.error("未找到规则信息: ruleId={}", hitResult.getRuleId());
			return;
		}else {
			hitEntity.setRefreshTime(new Date(System.currentTimeMillis() + hitResult.getDeferMinutes() * 60 * 1000L));
		}
		hitEntity.setBlackListId(blackListId);
		hitEntity.setRuleId(hitResult.getRuleId());
		hitEntity.setObjectId(hitResult.getObjectId());
		hitEntity.setPoint(hitResult.getPoint());
		hitEntity.setContext(hitResult.getContext());
		hitEntity.setCreateTime(hitResult.getHitTime());
		hitEntity.setUpdateTime(hitResult.getHitTime());
		rcBlackListReasonHitEntityRepository.save(hitEntity);
	}

}
