package com.decathlon.sino.controller;

import com.decathlon.sino.model.input.AuditSceneInput;
import com.decathlon.sino.model.input.RuleInput;
import com.decathlon.sino.model.ouput.AuditSceneOutput;
import com.decathlon.sino.service.AuditRuleManagerService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AuditRuleBackOfficeController.class)
class AuditRuleBackOfficeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AuditRuleManagerService auditRuleManagerService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testCreateAuditScene() throws Exception {
        // Given
        AuditSceneInput input = new AuditSceneInput();
        input.setClassification("TEST");
        input.setSceneName("测试场景");
        input.setSceneDesc("测试场景描述");
        input.setInterceptStatus(true);
        input.setEnableStatus(true);

        AuditSceneOutput output = new AuditSceneOutput();
        output.setSceneId(1L);
        output.setClassification("TEST");
        output.setSceneName("测试场景");
        output.setSceneDesc("测试场景描述");
        output.setInterceptStatus(true);
        output.setEnableStatus(true);
        output.setUpdateTime(new Date());

        when(auditRuleManagerService.createAuditScene(any(AuditSceneInput.class))).thenReturn(output);

        // When & Then
        mockMvc.perform(post("/scene")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(input)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.scene_id").value(1L))
                .andExpect(jsonPath("$.classification").value("TEST"))
                .andExpect(jsonPath("$.scene_name").value("测试场景"))
                .andExpect(jsonPath("$.scene_desc").value("测试场景描述"))
                .andExpect(jsonPath("$.intercept_status").value(true))
                .andExpect(jsonPath("$.enable_status").value(true));
    }

    @Test
    void testGetScenes() throws Exception {
        // Given
        AuditSceneOutput scene1 = new AuditSceneOutput();
        scene1.setSceneId(1L);
        scene1.setClassification("POINT");
        scene1.setSceneName("积分场景");
        scene1.setEnableStatus(true);

        AuditSceneOutput scene2 = new AuditSceneOutput();
        scene2.setSceneId(2L);
        scene2.setClassification("PURCHASE");
        scene2.setSceneName("购买场景");
        scene2.setEnableStatus(true);

        when(auditRuleManagerService.getScences()).thenReturn(Arrays.asList(scene1, scene2));

        // When & Then
        mockMvc.perform(get("/scene"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].scene_id").value(1L))
                .andExpect(jsonPath("$[0].classification").value("POINT"))
                .andExpect(jsonPath("$[0].scene_name").value("积分场景"))
                .andExpect(jsonPath("$[1].scene_id").value(2L))
                .andExpect(jsonPath("$[1].classification").value("PURCHASE"))
                .andExpect(jsonPath("$[1].scene_name").value("购买场景"));
    }

    @Test
    void testCreateRule() throws Exception {
        // Given
        RuleInput input = new RuleInput();
        input.setRuleName("测试规则");
        input.setRuleDesc("测试规则描述");
        input.setTriggerType("POINT");
        input.setExpression("pointBalance > 1000");
        input.setStatus(true);
        input.setCreateBy("test_user");

        // When & Then
        mockMvc.perform(post("/rule")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(input)))
                .andExpect(status().isOk());
    }

    @Test
    void testGetRules() throws Exception {
        // Given
        when(auditRuleManagerService.getRules(null, null, null)).thenReturn(Collections.emptyList());

        // When & Then
        mockMvc.perform(get("/rule"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }

    @Test
    void testGetRulesWithParameters() throws Exception {
        // Given
        when(auditRuleManagerService.getRules("测试规则", 1L, true)).thenReturn(Collections.emptyList());

        // When & Then
        mockMvc.perform(get("/rule")
                .param("rule_name", "测试规则")
                .param("rule_id", "1")
                .param("status", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testGetSceneDetail() throws Exception {
        // Given
        Long sceneId = 1L;
        when(auditRuleManagerService.getSceneDetail(sceneId)).thenReturn(new com.decathlon.sino.model.ouput.AuditSceneDetailOutput());

        // When & Then
        mockMvc.perform(get("/scene/detail")
                .param("scene_id", sceneId.toString()))
                .andExpect(status().isOk());
    }

    @Test
    void testGetRuleDetail() throws Exception {
        // Given
        Long ruleId = 1L;
        when(auditRuleManagerService.getRuleDetail(ruleId)).thenReturn(new com.decathlon.sino.model.ouput.AuditRuleDetailOutput());

        // When & Then
        mockMvc.perform(get("/rule/detail")
                .param("rule_id", ruleId.toString()))
                .andExpect(status().isOk());
    }

    @Test
    void testAddRule() throws Exception {
        // When & Then
        mockMvc.perform(post("/scene/rule")
                .param("rule_id", "1")
                .param("scene_id", "1"))
                .andExpect(status().isOk());
    }

    @Test
    void testDeleteRule() throws Exception {
        // When & Then
        mockMvc.perform(delete("/scene/rule")
                .param("rule_id", "1"))
                .andExpect(status().isOk());
    }
}
