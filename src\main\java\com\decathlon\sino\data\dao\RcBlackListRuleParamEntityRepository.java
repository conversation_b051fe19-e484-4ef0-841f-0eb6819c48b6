package com.decathlon.sino.data.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.decathlon.sino.data.dao.basic.QueryDslBaseDao;
import com.decathlon.sino.data.entity.RcBlackListRuleParamEntity;

public interface RcBlackListRuleParamEntityRepository extends QueryDslBaseDao<RcBlackListRuleParamEntity> {

	@Query(value = "select * from rc_black_list_rule_param where rule_id in :collect", nativeQuery = true)
	List<RcBlackListRuleParamEntity> findByRuleIds(List<Long> collect);
	
	@Query(value = "select * from rc_black_list_rule_param where shared = true", nativeQuery = true)
	List<RcBlackListRuleParamEntity> findAllSharedParam();

}
