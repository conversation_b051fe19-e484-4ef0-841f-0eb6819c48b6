package com.decathlon.sino.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import com.decathlon.sino.common.exception.ServiceException;
import com.decathlon.sino.data.dao.RcBlackListRuleEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListRuleParamEntityRepository;
import com.decathlon.sino.data.dao.RcScenceRuleEntityRepository;
import com.decathlon.sino.data.entity.RcBlackListRuleEntity;
import com.decathlon.sino.data.entity.RcBlackListRuleParamEntity;
import com.decathlon.sino.data.entity.RcScenceRuleEntity;
import com.decathlon.sino.model.bo.ParamDef;
import com.decathlon.sino.model.bo.RuleDefinition;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 规则缓存组件 - 负责加载和缓存规则定义
 *
 * 主要功能：
 * 1. 启动时初始化缓存所有启用的规则
 * 2. 按场景ID缓存规则，提高查询性能
 * 3. 支持缓存刷新和失效
 * 4. 线程安全的缓存操作
 *
 * 缓存策略：
 * - 全局规则缓存：Map<Long, RuleDefinition> (ruleId -> RuleDefinition)
 * - 场景规则缓存：Map<Long, List<RuleDefinition>> (sceneId -> List<RuleDefinition>)
 */
@Component
@AllArgsConstructor
@Slf4j
public class RuleCacheComponent {

	private final RcBlackListRuleEntityRepository rcBlackListRuleEntityRepository;
	private final RcBlackListRuleParamEntityRepository rcBlackListRuleParamEntityRepository;
	private final RcScenceRuleEntityRepository rcScenceRuleEntityRepository;

	// 全局规则缓存 - 线程安全
	private final Map<Long, RuleDefinition> globalRulesCache = new ConcurrentHashMap<>();

	// 场景规则缓存 - 线程安全
	private final Map<Long, List<RuleDefinition>> sceneRulesCache = new ConcurrentHashMap<>();

	// JSON解析器
	private final ObjectMapper objectMapper = new ObjectMapper();

	/**
	 * 系统启动时初始化缓存
	 */
	@PostConstruct
	public void init() {
		log.info("初始化规则缓存组件...");
		reloadAll();
		log.info("规则缓存组件初始化完成，共加载 {} 个规则", globalRulesCache.size());
	}

	/**
	 * 重新加载所有规则缓存
	 * 线程安全的缓存刷新操作
	 */
	@CacheEvict(value = {"rules", "sceneRules"}, allEntries = true)
	public synchronized void reloadAll() {
		log.info("开始重新加载所有规则缓存...");

		try {
			// 1. 获取所有启用的规则
			List<RcBlackListRuleEntity> enabledRules = rcBlackListRuleEntityRepository.findEnabled();
			log.debug("找到 {} 个启用的规则", enabledRules.size());

			if (enabledRules.isEmpty()) {
				log.warn("没有找到启用的规则");
				clearAllCaches();
				return;
			}

			// 2. 获取规则参数
			List<Long> ruleIds = enabledRules.stream().map(RcBlackListRuleEntity::getId).toList();
			List<RcBlackListRuleParamEntity> ruleParams = rcBlackListRuleParamEntityRepository.findByRuleIds(ruleIds);

			// 3. 按规则ID分组参数
			Map<Long, List<RcBlackListRuleParamEntity>> paramsByRuleId = ruleParams.stream()
					.collect(Collectors.groupingBy(RcBlackListRuleParamEntity::getRuleId));

			// 4. 获取场景规则关联关系
			List<RcScenceRuleEntity> sceneRuleRelations = rcScenceRuleEntityRepository.findAll();
			Map<Long, List<RcScenceRuleEntity>> relationsBySceneId = sceneRuleRelations.stream()
					.collect(Collectors.groupingBy(RcScenceRuleEntity::getSceneId));

			// 5. 构建全局规则缓存
			Map<Long, RuleDefinition> newGlobalCache = new ConcurrentHashMap<>();
			Map<Long, List<RuleDefinition>> newSceneCache = new ConcurrentHashMap<>();

			for (RcBlackListRuleEntity rule : enabledRules) {
				RuleDefinition ruleDefinition = buildRuleDefinition(rule, paramsByRuleId.get(rule.getId()));
				newGlobalCache.put(rule.getId(), ruleDefinition);
			}

			// 6. 构建场景规则缓存
			for (Map.Entry<Long, List<RcScenceRuleEntity>> entry : relationsBySceneId.entrySet()) {
				Long sceneId = entry.getKey();
				List<RuleDefinition> sceneRules = entry.getValue().stream()
						.map(relation -> {
							RuleDefinition rule = newGlobalCache.get(relation.getRuleId());
							if (rule != null) {
								// 创建带有场景权重的规则副本
								return new RuleDefinition(
										rule.getRuleId(),
										rule.getExpr(),
										BigDecimal.valueOf(relation.getPoint()),
										rule.getParams()
								);
							}
							return null;
						})
						.filter(rule -> rule != null)
						.toList();

				newSceneCache.put(sceneId, sceneRules);
			}

			// 7. 原子性更新缓存
			globalRulesCache.clear();
			globalRulesCache.putAll(newGlobalCache);

			sceneRulesCache.clear();
			sceneRulesCache.putAll(newSceneCache);

			log.info("规则缓存重新加载完成 - 全局规则: {}, 场景缓存: {}",
					globalRulesCache.size(), sceneRulesCache.size());

		} catch (Exception e) {
			log.error("重新加载规则缓存失败", e);
			throw new ServiceException("规则缓存加载失败", e);
		}
	}

	/**
	 * 构建规则定义对象
	 */
	private RuleDefinition buildRuleDefinition(RcBlackListRuleEntity rule, List<RcBlackListRuleParamEntity> params) {
		List<ParamDef> paramDefs = new ArrayList<>();

		if (params != null) {
			for (RcBlackListRuleParamEntity param : params) {
				Object defaultValue = null;
				if (StringUtils.isNotBlank(param.getDefaultValue())) {
					defaultValue = parseDefaultValue(param.getDefaultValue(), param.getParamType());
				}
				paramDefs.add(new ParamDef(param.getParamName(), defaultValue, param.getEnableConfig()));
			}
		}

		return new RuleDefinition(
				rule.getId(),
				rule.getExpression(),
				null, // 权重分数在场景缓存中设置
				paramDefs
		);
	}

	/**
	 * 解析参数默认值
	 */
	private Object parseDefaultValue(String value, String type) {
		if (StringUtils.isBlank(value) || StringUtils.isBlank(type)) {
			return value;
		}

		try {
			switch (type.toLowerCase()) {
				case "number","decimal":
					return new BigDecimal(value);
				case "boolean":
					return Boolean.parseBoolean(value);
				case "list","array":
					return objectMapper.readValue(value, new TypeReference<List<String>>() {});
				case "long":
					return Long.parseLong(value);
				case "integer","int":
					return Integer.parseInt(value);
				case "double":
					return Double.parseDouble(value);
				case "float":
					return Float.parseFloat(value);
				case "string":
				default:
					return value;
			}
		} catch (Exception e) {
			log.warn("解析参数默认值失败: value={}, type={}, error={}", value, type, e.getMessage());
			return value; // 解析失败时返回原始字符串
		}
	}

	/**
	 * 获取所有规则缓存
	 */
	@Cacheable(value = "rules", key = "'all'")
	public Map<Long, RuleDefinition> getAllRules() {
		return new HashMap<>(globalRulesCache);
	}

	/**
	 * 根据规则ID获取规则定义
	 */
	@Cacheable(value = "rules", key = "#ruleId")
	public RuleDefinition getRuleById(Long ruleId) {
		return globalRulesCache.get(ruleId);
	}

	/**
	 * 根据场景ID获取该场景下的所有规则
	 */
	@Cacheable(value = "sceneRules", key = "#sceneId")
	public List<RuleDefinition> getRulesBySceneId(Long sceneId) {
		return sceneRulesCache.getOrDefault(sceneId, Collections.emptyList());
	}

	/**
	 * 检查规则是否存在且启用
	 */
	public boolean isRuleEnabled(Long ruleId) {
		return globalRulesCache.containsKey(ruleId);
	}

	/**
	 * 检查场景是否有关联的规则
	 */
	public boolean hasRulesForScene(Long sceneId) {
		List<RuleDefinition> rules = sceneRulesCache.get(sceneId);
		return rules != null && !rules.isEmpty();
	}

	/**
	 * 获取场景规则数量
	 */
	public int getSceneRuleCount(Long sceneId) {
		List<RuleDefinition> rules = sceneRulesCache.get(sceneId);
		return rules != null ? rules.size() : 0;
	}

	/**
	 * 获取缓存统计信息
	 */
	public Map<String, Object> getCacheStats() {
		Map<String, Object> stats = new HashMap<>();
		stats.put("totalRules", globalRulesCache.size());
		stats.put("totalScenes", sceneRulesCache.size());
		stats.put("sceneRuleCounts", sceneRulesCache.entrySet().stream()
				.collect(Collectors.toMap(
						entry -> "scene_" + entry.getKey(),
						entry -> entry.getValue().size()
				)));
		return stats;
	}

	/**
	 * 清空所有缓存
	 */
	@CacheEvict(value = {"rules", "sceneRules"}, allEntries = true)
	public void clearAllCaches() {
		globalRulesCache.clear();
		sceneRulesCache.clear();
		log.info("所有规则缓存已清空");
	}

	/**
	 * 刷新单个规则缓存
	 */
	@CacheEvict(value = "rules", key = "#ruleId")
	public void refreshRule(Long ruleId) {
		log.info("刷新规则缓存: ruleId={}", ruleId);

		try {
			RcBlackListRuleEntity rule = rcBlackListRuleEntityRepository.findById(ruleId).orElse(null);
			if (rule == null || !rule.getStatus()) {
				// 规则不存在或已禁用，从缓存中移除
				globalRulesCache.remove(ruleId);
				removeRuleFromSceneCaches(ruleId);
				log.info("规则 {} 已从缓存中移除", ruleId);
				return;
			}

			// 重新构建规则定义
			List<RcBlackListRuleParamEntity> params = rcBlackListRuleParamEntityRepository.findByRuleIds(List.of(ruleId));
			RuleDefinition ruleDefinition = buildRuleDefinition(rule, params);
			globalRulesCache.put(ruleId, ruleDefinition);

			// 更新场景缓存中的规则
			updateRuleInSceneCaches(ruleId, ruleDefinition);

			log.info("规则 {} 缓存已刷新", ruleId);
		} catch (Exception e) {
			log.error("刷新规则缓存失败: ruleId={}", ruleId, e);
		}
	}
	
	/**
	 * 刷新单个规则缓存
	 */
	@CacheEvict(value = "single_rule", key = "#ruleId")
	public RcBlackListRuleEntity getRule(Long ruleId) {
		log.info("获取单个rule,ruleId={}", ruleId);
		return rcBlackListRuleEntityRepository.findById(ruleId).orElse(null);
	}

	/**
	 * 刷新场景规则缓存
	 */
	@CacheEvict(value = "sceneRules", key = "#sceneId")
	public void refreshSceneRules(Long sceneId) {
		log.info("刷新场景规则缓存: sceneId={}", sceneId);

		try {
			List<RcScenceRuleEntity> sceneRules = rcScenceRuleEntityRepository.findBySceneId(sceneId);
			List<RuleDefinition> ruleDefinitions = sceneRules.stream()
					.map(relation -> {
						RuleDefinition rule = globalRulesCache.get(relation.getRuleId());
						if (rule != null) {
							return new RuleDefinition(
									rule.getRuleId(),
									rule.getExpr(),
									BigDecimal.valueOf(relation.getPoint()),
									rule.getParams()
							);
						}
						return null;
					})
					.filter(rule -> rule != null)
					.toList();

			sceneRulesCache.put(sceneId, ruleDefinitions);
			log.info("场景 {} 规则缓存已刷新，共 {} 个规则", sceneId, ruleDefinitions.size());
		} catch (Exception e) {
			log.error("刷新场景规则缓存失败: sceneId={}", sceneId, e);
		}
	}

	/**
	 * 从场景缓存中移除指定规则
	 */
	private void removeRuleFromSceneCaches(Long ruleId) {
		sceneRulesCache.entrySet().forEach(entry -> {
			List<RuleDefinition> rules = entry.getValue();
			rules.removeIf(rule -> rule.getRuleId().equals(ruleId));
		});
	}

	/**
	 * 更新场景缓存中的规则
	 */
	private void updateRuleInSceneCaches(Long ruleId, RuleDefinition newRuleDefinition) {
		// 获取该规则关联的所有场景
		List<RcScenceRuleEntity> relations = rcScenceRuleEntityRepository.findByRuleId(ruleId);

		for (RcScenceRuleEntity relation : relations) {
			Long sceneId = relation.getSceneId();
			List<RuleDefinition> sceneRules = sceneRulesCache.get(sceneId);

			if (sceneRules != null) {
				// 移除旧的规则定义
				sceneRules.removeIf(rule -> rule.getRuleId().equals(ruleId));

				// 添加新的规则定义（带权重）
				RuleDefinition ruleWithWeight = new RuleDefinition(
						newRuleDefinition.getRuleId(),
						newRuleDefinition.getExpr(),
						BigDecimal.valueOf(relation.getPoint()),
						newRuleDefinition.getParams()
				);
				sceneRules.add(ruleWithWeight);
			}
		}
	}
}
