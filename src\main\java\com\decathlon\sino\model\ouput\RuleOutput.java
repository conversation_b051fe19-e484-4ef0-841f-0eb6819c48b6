package com.decathlon.sino.model.ouput;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RuleOutput {
	
    private Long id;

    private String bizType;
    private Long bizTypeId;
    private String triggerType;
    private Long triggerTypeId;
    
    private String ruleName;
    private String expression;
    private String description;
    private BigDecimal point;
    private Boolean status;
    
    private String createTime;
    private String updateTime;
    private String createBy;
    private String updateBy;
}
