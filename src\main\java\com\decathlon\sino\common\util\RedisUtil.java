package com.decathlon.sino.common.util;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * redis operation util
 */
public class RedisUtil {

    @SuppressWarnings("unchecked")
    private static final RedisTemplate<String, Object> redisTemplate =
            SpringContextUtil.getBean("redisTemplate", RedisTemplate.class);

    /**
     * 设置key过期时间
     * @param key
     * @param seconds
     */
    public static void expire(String key, long seconds) {
        redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
    }

    /**
     * 读取String类型缓存
     * @param key
     * @return
     */
    public static String get(final String key) {
        return (String) redisTemplate.opsForValue().get(key);
    }

    /**
     * 根据前缀获取所有的key
     * @param prefix
     * @return
     */
    public static Set<String> getListKey(String prefix) {
        Set<String> keys = redisTemplate.keys(prefix.concat("*"));
        return keys;
    }

    /**
     * 读取缓存转化为指定的类型
     * @param <V>
     * @param key
     * @param clazz
     * @return
     */
    public static <V> V get(final String key, final Class<V> clazz) {
        return clazz.cast(redisTemplate.opsForValue().get(key));
    }

    /**
     * 读取缓存转化为指定的类型
     * @param <V>
     * @param key
     * @param clazz
     * @return
     */
    public static <V> V getFromJson(final String key, final Class<V> clazz) {
        String data = (String) redisTemplate.opsForValue().get(key);
        if (data == null) return null;
        return JsonUtil.fromJson(data, clazz);
    }

    /**
     * 设置缓存
     * @param key
     * @param value
     */
    public static void set(final String key, final Object value) {
        redisTemplate.opsForValue().set(key, value);
    }


    /**
     * 删除key
     * @param key
     */
    public static void delete(final String key) {
        redisTemplate.delete(key);
    }

    /**
     * 设置缓存以及过期时间
     * @param key
     * @param value
     */
    public static void set(final String key, final Object value, long seconds) {
        redisTemplate.opsForValue().set(key, value, seconds, TimeUnit.SECONDS);
    }

    /**
     * 判断缓存中是否有对应的value
     * @param key
     * @return
     */
    public static boolean exists(final String key) {
        Boolean exists = redisTemplate.hasKey(key);
        return exists != null && exists;
    }

    /**
     * 设置Hash类型缓存
     * @param key
     * @param HashKey
     * @param HashValue
     */
    public static void setHash(final String key, final String HashKey, Object HashValue) {
        redisTemplate.opsForHash().put(key, HashKey, HashValue);
    }

    /**
     * 读取Hash类型缓存
     * @param key
     * @return
     */
    public static Object getHash(final String key, final String HashKey) {
        return redisTemplate.opsForHash().get(key, HashKey);
    }

    /**
     * 根据key删除hash缓存
     * @param key
     */
    public static void delHash(final String key, final String hashKey) {
        redisTemplate.opsForHash().delete(key, hashKey);
    }

    /**
     * 查询hash中key是否存在
     * @param key
     * @param hashKey
     * @return
     */
    public static boolean hasHash(final String key, final String hashKey) {
        return key != null && hashKey != null & redisTemplate.opsForHash().hasKey(key, hashKey);
    }

    /**
     * zset添加元素
     * @param key
     * @param value
     * @param score
     */
    public static void sortedSetAdd(final String key, final Object value, double score) {
        redisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * zset添加元素
     * @param key
     * @param set
     */
    public static void sortedSetAdd(final String key, Set<ZSetOperations.TypedTuple<Object>> set) {
        redisTemplate.opsForZSet().add(key, set);
    }

    /**
     * 根据score区间获取zset的元素
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static Set<Object> rangeByScore(final String key, double start, double end) {
        return redisTemplate.opsForZSet().rangeByScore(key, start, end);
    }

    /**
     * 根据score区间删除zset的元素
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static Long removeRangeByScore(final String key, double start, double end) {
        return redisTemplate.opsForZSet().removeRangeByScore(key, start, end);
    }

    /**
     * 根据index查询zset中的元素
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static Set<Object> range(final String key, long start, long end) {
        return redisTemplate.opsForZSet().range(key, start, end);
    }

    /**
     * 根据index查询zset中的元素
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static Set<Object> reverseRange(final String key, long start, long end) {
        return redisTemplate.opsForZSet().reverseRange(key, start, end);
    }

    /**
     * 根据index查询zet中的元素和score
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static Set<ZSetOperations.TypedTuple<Object>> reverseRangeWithScores(final String key, long start, long end) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(key, start, end);
    }

    /**
     * 查询zset中的元素数量
     * @param key
     * @return
     */
    public static Long sizeOfZSet(final String key) {
        return redisTemplate.opsForZSet().size(key);
    }

    /**
     * RPUSH key value1
     * @param key
     * @param value
     * @return
     */
    public static Long listRightPush(final String key, Object value) {
        return redisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * LRANGE key start stop
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static List<Object> listRange(final String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    /**
     * LTRIM key start stop
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static void listTrim(final String key, long start, long end) {
        redisTemplate.opsForList().trim(key, start, end);
    }

    /**
     * LSIZE key start stop
     * @param key
     * @return
     */
    public static Long listSize(final String key) {
        return redisTemplate.opsForList().size(key);
    }

    /**
     * list lrem
     * @param key
     * @param count
     * @param value
     * @return
     */
    public static Long removeListVal(final String key, long count, Object value) {
        return redisTemplate.opsForList().remove(key, count, value);
    }

    /**
     * 获取key的过期时间
     * return -2 if the key does not exist.
     * return -1 if the key exists but has no associated expire.
     * @param key
     * @return
     */
    public static Long getExpire(final String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * @param key
     * @return
     */
    public static Boolean setContain(final String key, Object value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }

    /**
     * @param key
     * @return
     */
    public static Long setAdd(final String key, Object... values) {
        return redisTemplate.opsForSet().add(key, values);
    }

    /**
     * @param key
     * @return
     */
    public static Long setRemove(final String key, Object... values) {
        return redisTemplate.opsForSet().remove(key, values);
    }

    /**
     * @param key
     * @return
     */
    public static Long setSize(final String key) {
        return redisTemplate.opsForSet().size(key);
    }

    /**
     * set and expiry time out if key is not exist
     * @param key
     * @param value
     * @param time
     * @param timeUnit
     */
    public static Boolean setIfAbsent(final String key, final Object value, long time, TimeUnit timeUnit) {
        return redisTemplate.opsForValue().setIfAbsent(key, value, time, timeUnit);
    }

    public static Object getAndSet(final String key, final Object value) {
        return redisTemplate.opsForValue().getAndSet(key, value);
    }

    /**
     * decrBy command
     * @param key
     * @param value
     * @return
     */
    public static Long decrBy(final String key, Long value) {
        return redisTemplate.opsForValue().decrement(key, value);
    }

    /**
     * incrBy command
     * @param key
     * @param value
     * @return
     */
    public static Long incrBy(final String key, Long value) {
        return redisTemplate.opsForValue().increment(key, value);
    }
}
