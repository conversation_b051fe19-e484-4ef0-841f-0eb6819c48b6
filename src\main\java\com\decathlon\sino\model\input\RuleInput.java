package com.decathlon.sino.model.input;

import java.util.List;

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表达式构建结果DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(SnakeCaseStrategy.class)
@ApiModel(description = "创建规则")
public class RuleInput {
	
	@ApiModelProperty(value = "规则ID")
	private Long ruleId;
	
	@ApiModelProperty(value = "规则名称")
	private String ruleName;

	@ApiModelProperty(value = "规则说明")
	private String ruleDesc;
	
	@ApiModelProperty(value = "业务类型")
	private String sceneType;
	
	private String triggerType;
	
	private Integer point;
	
	private Boolean status;
	
	private String createBy;
	
	private String updateBy;
	
	private Integer deferMinutes;
	
    @ApiModelProperty(value = "生成的Spring表达式")
    private String expression;

    @ApiModelProperty(value = "表达式描述")
    private String description;
    
    @ApiModelProperty(value = "相关参数")
    private List<RuleParamInput> systemParams;
}
