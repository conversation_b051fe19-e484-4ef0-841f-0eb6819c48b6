package com.decathlon.sino.service.impl;

import com.decathlon.sino.model.criteria.BlackListSearchCriteria;
import com.decathlon.sino.model.criteria.HitRecordSearchCriteria;
import com.decathlon.sino.model.dto.AuditParamDto;
import com.decathlon.sino.model.dto.AuditRuleBaseInfoDto;
import com.decathlon.sino.model.input.AuditSceneInput;
import com.decathlon.sino.model.input.RuleInput;
import com.decathlon.sino.model.input.RuleParamInput;
import com.decathlon.sino.model.input.SceneRuleInput;
import com.decathlon.sino.model.ouput.AuditRuleDetailOutput;
import com.decathlon.sino.model.ouput.AuditSceneDetailOutput;
import com.decathlon.sino.model.ouput.AuditSceneOutput;
import com.decathlon.sino.model.ouput.RuleBlackListOutput;
import com.decathlon.sino.model.ouput.RuleHitRecordOutput;
import com.decathlon.sino.service.AuditRuleManagerService;
import com.decathlon.sino.data.dao.*;
import com.decathlon.sino.data.entity.*;
import com.decathlon.sino.common.config.PageResultDTO;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.decathlon.sino.data.entity.QRcBizScenceEntity.rcBizScenceEntity;
import static com.decathlon.sino.data.entity.QRcBlackListEntity.rcBlackListEntity;
import static com.decathlon.sino.data.entity.QRcBlackListReasonHitEntity.rcBlackListReasonHitEntity;
import static com.decathlon.sino.data.entity.QRcBlackListRuleEntity.rcBlackListRuleEntity;
import static com.decathlon.sino.data.entity.QRcScenceRuleEntity.rcScenceRuleEntity;

@Service
@AllArgsConstructor
@Slf4j
public class AuditRuleManagerServiceImpl implements AuditRuleManagerService {

    private static final String RULE_DESC = "ruleDesc";
	private final RcBlackListRuleEntityRepository rcBlackListRuleEntityRepository;
    private final RcBlackListRuleParamEntityRepository rcBlackListRuleParamEntityRepository;
    private final RcBlackListEntityRepository rcBlackListEntityRepository;
    private final RcBizScenceEntityRepository rcBizScenceEntityRepository;
    private final RcScenceRuleEntityRepository rcScenceRuleEntityRepository;
    private final JPAQueryFactory queryFactory;


    @Override
    @Transactional
    public void createRule(RuleInput ruleInput) {
        log.info("创建规则: {}", ruleInput);

        RcBlackListRuleEntity entity = new RcBlackListRuleEntity();
        entity.setRuleName(ruleInput.getRuleName());
        entity.setTriggerType(ruleInput.getTriggerType());
        entity.setExpression(ruleInput.getExpression());
        entity.setDescription(ruleInput.getDescription());
        entity.setStatus(ruleInput.getStatus());
        entity.setCreateBy(ruleInput.getCreateBy());
        entity.setUpdateBy(ruleInput.getUpdateBy());
        entity.setCreateTime(new Date());
        entity.setDeferMinutes(ruleInput.getDeferMinutes());;
        entity.setUpdateTime(new Date());

        RcBlackListRuleEntity savedRule = rcBlackListRuleEntityRepository.save(entity);

        // 保存规则参数
        if (ruleInput.getSystemParams() != null && !ruleInput.getSystemParams().isEmpty()) {
            List<RcBlackListRuleParamEntity> params = ruleInput.getSystemParams().stream().map(param ->
                RcBlackListRuleParamEntity.builder()
                        .ruleId(savedRule.getId())
                        .paramName(param.getParamName())
                        .paramType(param.getParamType())
                        .defaultValue(param.getDefaultValue())
                        .description(param.getDescription())
                        .enableConfig(param.getEnableConfig())
                        .shared(false)
                        .createTime(new Date())
                        .updateTime(new Date())
                        .build()
            ).collect(Collectors.toList());

            rcBlackListRuleParamEntityRepository.saveAll(params);
        }
    }

    @Override
    @Transactional
    public void updateRule(RuleInput ruleInput) {
        log.info("更新规则: {}", ruleInput);

        if (ruleInput.getRuleId() == null) {
            throw new IllegalArgumentException("ruleId不能为空");
        }

        RcBlackListRuleEntity entity = rcBlackListRuleEntityRepository.findById(ruleInput.getRuleId())
                .orElseThrow(() -> new IllegalArgumentException("未找到对应的规则: " + ruleInput.getRuleId()));

        if (StringUtils.hasText(ruleInput.getRuleName())) {
            entity.setRuleName(ruleInput.getRuleName());
        }
        if (StringUtils.hasText(ruleInput.getTriggerType())) {
            entity.setTriggerType(ruleInput.getTriggerType());
        }
        if (StringUtils.hasText(ruleInput.getExpression())) {
            entity.setExpression(ruleInput.getExpression());
        }
        if (StringUtils.hasText(ruleInput.getDescription())) {
            entity.setDescription(ruleInput.getDescription());
        }
        verificationChangeStatus(ruleInput, entity);
        if (StringUtils.hasText(ruleInput.getUpdateBy())) {
            entity.setUpdateBy(ruleInput.getUpdateBy());
        }
        if (ruleInput.getDeferMinutes() != null) {
			entity.setDeferMinutes(ruleInput.getDeferMinutes());
		}
        entity.setUpdateTime(new Date());

        rcBlackListRuleEntityRepository.save(entity);

        // 更新规则参数
        if (ruleInput.getSystemParams() != null) {
            // 删除原有参数
            List<RcBlackListRuleParamEntity> existingParams = rcBlackListRuleParamEntityRepository.findByRuleIds(Arrays.asList(ruleInput.getRuleId()));
            rcBlackListRuleParamEntityRepository.deleteAll(existingParams);

            // 保存新参数
            if (!ruleInput.getSystemParams().isEmpty()) {
                List<RcBlackListRuleParamEntity> newParams = ruleInput.getSystemParams().stream().map(param ->
                    RcBlackListRuleParamEntity.builder()
                            .ruleId(ruleInput.getRuleId())
                            .paramName(param.getParamName())
                            .paramType(param.getParamType())
                            .defaultValue(param.getValue())
                            .description(param.getDescription())
                            .enableConfig(param.getEnableConfig())
                            .shared(false)
                            .createTime(new Date())
                            .updateTime(new Date())
                            .build()
                ).collect(Collectors.toList());

                rcBlackListRuleParamEntityRepository.saveAll(newParams);
            }
        }
    }

	private void verificationChangeStatus(RuleInput ruleInput, RcBlackListRuleEntity entity) {
		if (ruleInput.getStatus() != null) {
            // 如果从启用状态修改为禁用状态，需要验证当前规则没有被场景关联
            if (Boolean.TRUE.equals(entity.getStatus()) && Boolean.FALSE.equals(ruleInput.getStatus())) {
                List<RcScenceRuleEntity> sceneRules = rcScenceRuleEntityRepository.findByRuleId(ruleInput.getRuleId());
                if (!sceneRules.isEmpty()) {
                    throw new IllegalStateException("规则正在被场景使用，无法禁用。请先解除场景绑定关系。");
                }
            }
            entity.setStatus(ruleInput.getStatus());
        }
	}

    @Override
    public List<RuleParamInput> getAllRuleParams() {
        // 只返回共享参数（shared = true）
        return rcBlackListRuleParamEntityRepository.findAll().stream()
                .filter(e -> Boolean.TRUE.equals(e.getShared()))
                .map(e -> {
                    RuleParamInput input = new RuleParamInput();
                    input.setId(e.getId());
                    input.setParamName(e.getParamName());
                    input.setParamType(e.getParamType());
                    input.setDefaultValue(e.getDefaultValue());
                    input.setDescription(e.getDescription());
                    input.setEnableConfig(e.getEnableConfig());
                    return input;
                }).toList();
    }

    @Override
    public List<RuleHitRecordOutput> getRuleHitRecords(Long blackListId) {
        log.info("查询规则命中记录: blackListId={}", blackListId);

        // 1. 通过场景ID查询黑名单记录，再查询命中记录
        List<RuleHitRecordOutput> results = queryFactory
                .select(Projections.bean(RuleHitRecordOutput.class,
                        rcBlackListReasonHitEntity.objectId,
                        rcBlackListReasonHitEntity.createTime,
                        rcBlackListRuleEntity.ruleName,
                        rcBlackListRuleEntity.description.as(RULE_DESC),
                        rcBizScenceEntity.sceneName.as("sceneName")))
                .from(rcBlackListReasonHitEntity)
                .leftJoin(rcBlackListRuleEntity).on(rcBlackListReasonHitEntity.ruleId.eq(rcBlackListRuleEntity.id))
                .leftJoin(rcBlackListEntity).on(rcBlackListReasonHitEntity.blackListId.eq(rcBlackListEntity.id))
                .leftJoin(rcBizScenceEntity).on(rcBlackListEntity.sceneId.eq(rcBizScenceEntity.id))
                .where(rcBlackListReasonHitEntity.blackListId.eq(blackListId))
                .orderBy(rcBlackListReasonHitEntity.createTime.desc())
                .fetch();

        log.info("查询到 {} 条命中记录", results.size());
        return results;
    }



	@Override
	@Transactional
	public AuditSceneOutput createAuditScene(AuditSceneInput auditSceneInput) {
		log.info("创建审计场景: {}", auditSceneInput);

		RcBizScenceEntity bizType = RcBizScenceEntity.builder()
				.classification(auditSceneInput.getClassification())
				.sceneName(auditSceneInput.getSceneName())
				.description(auditSceneInput.getSceneDesc())
				.intercept(auditSceneInput.getInterceptStatus())
				.status(auditSceneInput.getEnableStatus())
				.createBy(auditSceneInput.getCreateBy())
				.createTime(new Date())
				.build();

		RcBizScenceEntity saved = rcBizScenceEntityRepository.save(bizType);

		AuditSceneOutput output = new AuditSceneOutput();
		output.setSceneId(saved.getId());
		output.setClassification(saved.getClassification());
		output.setSceneName(saved.getSceneName());
		output.setSceneDesc(saved.getDescription());
		output.setInterceptStatus(saved.getIntercept());
		output.setCreateBy(saved.getCreateBy());
		output.setEnableStatus(saved.getStatus());
		output.setUpdateTime(saved.getUpdateTime());

		return output;
	}

	@Override
	@Transactional
	public void updateAuditScene(AuditSceneInput auditSceneInput) {
		log.info("更新审计场景: {}", auditSceneInput);

		Optional<RcBizScenceEntity> optionalBizType = rcBizScenceEntityRepository.findById(auditSceneInput.getSceneId());
		if (optionalBizType.isEmpty()) {
			throw new IllegalArgumentException("场景不存在: " + auditSceneInput.getSceneId());
		}

		RcBizScenceEntity bizType = optionalBizType.get();
		if (StringUtils.hasText(auditSceneInput.getClassification())) {
			bizType.setClassification(auditSceneInput.getClassification());
		}
		if (StringUtils.hasText(auditSceneInput.getSceneName())) {
			bizType.setSceneName(auditSceneInput.getSceneName());
		}
		if (StringUtils.hasText(auditSceneInput.getSceneDesc())) {
			bizType.setDescription(auditSceneInput.getSceneDesc());
		}
		if (auditSceneInput.getInterceptStatus() != null) {
			bizType.setIntercept(auditSceneInput.getInterceptStatus());
		}
		if (auditSceneInput.getEnableStatus() != null) {
			bizType.setStatus(auditSceneInput.getEnableStatus());
		}
		bizType.setUpdateTime(new Date());

		rcBizScenceEntityRepository.save(bizType);
	}

	@Override
	@Transactional
	public void deleteRule(Long ruleId,Long sceneId) {
		log.info("deleteRule 绑定场景规则: ruleId={}, sceneId={}", ruleId, sceneId);

		// 检查规则是否存在
		if (!rcBlackListRuleEntityRepository.existsById(ruleId)) {
			throw new IllegalArgumentException("规则不存在: " + ruleId);
		}

		// 检查场景是否存在
		if (!rcBizScenceEntityRepository.existsById(sceneId)) {
			throw new IllegalArgumentException("场景不存在: " + sceneId);
		}

		// 检查是否已经绑定
		RcScenceRuleEntity existing = rcScenceRuleEntityRepository.findBySceneIdAndRuleId(sceneId, ruleId);
		if (existing == null) {
			throw new IllegalArgumentException("没有当前绑定关系: ruleId=" + ruleId + ", sceneId=" + sceneId);
		}
		rcScenceRuleEntityRepository.delete(existing);
	}

	@Override
	public List<AuditRuleBaseInfoDto> getRules(String ruleName, Long ruleId, Boolean status) {
		log.info("查询规则列表: ruleName={}, ruleId={}, status={}", ruleName, ruleId, status);

		return queryFactory
				.select(Projections.bean(AuditRuleBaseInfoDto.class,
						rcBlackListRuleEntity.id.as("ruleId"),
						rcBlackListRuleEntity.ruleName,
						rcBlackListRuleEntity.description.as(RULE_DESC),
						rcBlackListRuleEntity.status.as("enableStatus"),
						rcBlackListRuleEntity.updateTime,
						rcBlackListRuleEntity.triggerType))
				.from(rcBlackListRuleEntity)
				.where(buildRuleQueryCondition(ruleName, ruleId, status))
				.orderBy(rcBlackListRuleEntity.updateTime.desc())
				.fetch();
	}

	@Override
	public AuditSceneDetailOutput getSceneDetail(Long sceneId) {
		log.info("获取场景详情: {}", sceneId);

		Optional<RcBizScenceEntity> optionalBizType = rcBizScenceEntityRepository.findById(sceneId);
		if (optionalBizType.isEmpty()) {
			throw new IllegalArgumentException("场景不存在: " + sceneId);
		}

		RcBizScenceEntity bizType = optionalBizType.get();
		AuditSceneDetailOutput output = new AuditSceneDetailOutput();
		output.setSceneId(bizType.getId());
		output.setClassification(bizType.getClassification());
		output.setSceneName(bizType.getSceneName());
		output.setSceneDesc(bizType.getDescription());
		output.setInterceptStatus(bizType.getIntercept());
		output.setEnableStatus(bizType.getStatus());
		output.setUpdateTime(bizType.getUpdateTime());
		output.setCreateBy(bizType.getCreateBy());

		// 查询关联的规则列表
		List<RcScenceRuleEntity> sceneRules = rcScenceRuleEntityRepository.findBySceneId(sceneId);
		List<Long> raletionIds = sceneRules.stream().map(RcScenceRuleEntity::getId).toList();

		if (!raletionIds.isEmpty()) {
			List<AuditRuleBaseInfoDto> ruleList = queryFactory
					.select(Projections.bean(AuditRuleBaseInfoDto.class,
							rcBlackListRuleEntity.id.as("ruleId"),
							rcBlackListRuleEntity.ruleName,
							rcBlackListRuleEntity.description.as(RULE_DESC),
							rcScenceRuleEntity.point,
							rcBlackListRuleEntity.status.as("enableStatus"),
							rcBlackListRuleEntity.updateTime,
							rcBlackListRuleEntity.triggerType))
					.from(rcBlackListRuleEntity)
					.leftJoin(rcScenceRuleEntity).on(rcBlackListRuleEntity.id.eq(rcScenceRuleEntity.ruleId))
					.where(rcScenceRuleEntity.id.in(raletionIds))
					.fetch();
			output.setRuleList(ruleList);
			output.setRulePointSum(sceneRules.stream().mapToInt(RcScenceRuleEntity::getPoint).sum());
		} else {
			output.setRuleList(Collections.emptyList());
			output.setRulePointSum(0);
		}

		return output;
	}

	@Override
	public AuditRuleDetailOutput getRuleDetail(Long ruleId) {
		log.info("获取规则详情: {}", ruleId);

		Optional<RcBlackListRuleEntity> optionalRule = rcBlackListRuleEntityRepository.findById(ruleId);
		if (optionalRule.isEmpty()) {
			throw new IllegalArgumentException("规则不存在: " + ruleId);
		}

		RcBlackListRuleEntity rule = optionalRule.get();
		AuditRuleDetailOutput output = new AuditRuleDetailOutput();
		output.setRuleId(rule.getId());
		output.setRuleName(rule.getRuleName());
		output.setRuleDesc(rule.getDescription());
		output.setEnableStatus(rule.getStatus());
		output.setUpdateTime(rule.getUpdateTime());
		output.setTriggerType(rule.getTriggerType());
		output.setExpression(rule.getExpression());

		// 合并参数：已使用的参数 + 共享参数池中未使用的参数
		List<AuditParamDto> mergedParamList = mergeRuleParameters(ruleId);
		output.setParamList(mergedParamList);

		log.info("规则详情获取完成: ruleId={}, 参数总数={}", ruleId, mergedParamList.size());
		return output;
	}

	/**
	 * 合并规则参数：已使用的参数 + 共享参数池中未使用的参数
	 *
	 * @param ruleId 规则ID
	 * @return 合并后的参数列表
	 */
	private List<AuditParamDto> mergeRuleParameters(Long ruleId) {
		log.debug("开始合并规则参数: ruleId={}", ruleId);

		// 1. 查询规则已使用的参数
		List<RcBlackListRuleParamEntity> usedParams = rcBlackListRuleParamEntityRepository.findByRuleIds(Arrays.asList(ruleId));
		log.debug("规则已使用参数数量: {}", usedParams.size());

		// 2. 查询所有共享参数
		List<RcBlackListRuleParamEntity> allSharedParams = rcBlackListRuleParamEntityRepository.findAllSharedParam();
		log.debug("共享参数池总数: {}", allSharedParams.size());
		// 3. 构建已使用参数的名称到对象的映射
		Map<String, RcBlackListRuleParamEntity> usedParamMap = usedParams.stream()
				.collect(Collectors.toMap(
					RcBlackListRuleParamEntity::getParamName,
					param -> param,
					(existing, replacement) -> existing // 如果有重复的参数名，保留第一个
				));
		log.debug("已使用参数名称: {}", usedParamMap.keySet());
		List<AuditParamDto> resultList = new ArrayList<>();
		for (RcBlackListRuleParamEntity rcBlackListRuleParamEntity : allSharedParams) {
			if(!usedParamMap.containsKey(rcBlackListRuleParamEntity.getParamName())) {
				// 5. 对于未使用的共享参数，转换为 DTO 并添加到结果列表
				AuditParamDto dto = new AuditParamDto();
				dto.setParamId(rcBlackListRuleParamEntity.getId());
				dto.setParamName(rcBlackListRuleParamEntity.getParamName());
				dto.setParamType(rcBlackListRuleParamEntity.getParamType());
				dto.setDefaultValue(rcBlackListRuleParamEntity.getDefaultValue());
				dto.setValue(null); // 默认值作为初始值
				dto.setDescription(rcBlackListRuleParamEntity.getDescription());
				dto.setEnableConfig(rcBlackListRuleParamEntity.getEnableConfig());
				resultList.add(dto);
			} else {
				AuditParamDto dto = new AuditParamDto();
				dto.setParamId(rcBlackListRuleParamEntity.getId());
				dto.setParamName(rcBlackListRuleParamEntity.getParamName());
				dto.setParamType(rcBlackListRuleParamEntity.getParamType());
				dto.setDefaultValue(rcBlackListRuleParamEntity.getDefaultValue());
				dto.setValue(usedParamMap.get(rcBlackListRuleParamEntity.getParamName()).getDefaultValue());// 默认值作为初始值
				dto.setDescription(rcBlackListRuleParamEntity.getDescription());
				dto.setEnableConfig(rcBlackListRuleParamEntity.getEnableConfig());
				resultList.add(dto);
			}
		}
		return resultList;
	}


	@Override
	public List<AuditSceneOutput> getScences() {
		log.info("获取所有场景列表");

		List<RcBizScenceEntity> bizTypes = queryFactory
				.selectFrom(rcBizScenceEntity)
				.orderBy(rcBizScenceEntity.updateTime.desc())
				.fetch();

		return bizTypes.stream().map(bizType -> {
			AuditSceneOutput output = new AuditSceneOutput();
			output.setSceneId(bizType.getId());
			output.setClassification(bizType.getClassification());
			output.setSceneName(bizType.getSceneName());
			output.setSceneDesc(bizType.getDescription());
			output.setInterceptStatus(bizType.getIntercept());
			output.setEnableStatus(bizType.getStatus());
			output.setUpdateTime(bizType.getUpdateTime());
			output.setCreateBy(bizType.getCreateBy());
			return output;
		}).toList();
	}

	@Override
	public PageResultDTO<RuleBlackListOutput> getRuleBlackList(BlackListSearchCriteria searchCriteria) {

		Page<RcBlackListEntity> page =  rcBlackListEntityRepository.findAll(searchCriteria.toPredicate(), searchCriteria.getPageable());
		List<RuleBlackListOutput> content = page.getContent().stream().map(entity -> {
			RuleBlackListOutput output = new RuleBlackListOutput();
			output.setBlackListId(entity.getId());
			output.setCreateTime(entity.getCreateTime());
			output.setObjectId(entity.getObjectId());
			// Convert BigDecimal point to Integer totalScore
			output.setTotalScore(entity.getFinalScore());
			return output;
		}).toList();

		return PageResultDTO.of(page, content);
	}

	@Override
	public PageResultDTO<RuleHitRecordOutput> getRuleHitRecordsByRuleId(HitRecordSearchCriteria searchCriteria) {
		log.info("查询规则命中记录: {}", searchCriteria);

		// Use QueryDSL to join tables and get the required information
		List<RuleHitRecordOutput> content = queryFactory
				.select(Projections.bean(RuleHitRecordOutput.class,
						rcBlackListReasonHitEntity.objectId,
						rcBlackListReasonHitEntity.createTime,
						rcBlackListRuleEntity.ruleName,
						rcBlackListRuleEntity.description.as(RULE_DESC),
						rcBizScenceEntity.sceneName.as("sceneName")))
				.from(rcBlackListReasonHitEntity)
				.leftJoin(rcBlackListRuleEntity).on(rcBlackListReasonHitEntity.ruleId.eq(rcBlackListRuleEntity.id))
				.leftJoin(rcBlackListEntity).on(rcBlackListReasonHitEntity.blackListId.eq(rcBlackListEntity.id))
				.leftJoin(rcBizScenceEntity).on(rcBlackListEntity.sceneId.eq(rcBizScenceEntity.id))
				.where(buildHitRecordQueryCondition(searchCriteria))
				.orderBy(rcBlackListReasonHitEntity.createTime.desc())
				.offset(searchCriteria.getPageable().getOffset())
				.limit(searchCriteria.getPageable().getPageSize())
				.fetch();

		// Get total count for pagination
		long total = queryFactory
				.selectFrom(rcBlackListReasonHitEntity)
				.leftJoin(rcBlackListRuleEntity).on(rcBlackListReasonHitEntity.ruleId.eq(rcBlackListRuleEntity.id))
				.leftJoin(rcBlackListEntity).on(rcBlackListReasonHitEntity.blackListId.eq(rcBlackListEntity.id))
				.leftJoin(rcBizScenceEntity).on(rcBlackListEntity.sceneId.eq(rcBizScenceEntity.id))
				.where(buildHitRecordQueryCondition(searchCriteria))
				.fetch().size();

		Page<RuleHitRecordOutput> page = new PageImpl<>(content, searchCriteria.getPageable(), total);
		return PageResultDTO.of(page, content);
	}

	/**
	 * Build query condition for hit record search
	 */
	private Predicate buildHitRecordQueryCondition(HitRecordSearchCriteria searchCriteria) {
		Predicate predicate = rcBlackListReasonHitEntity.isNotNull();

		if (searchCriteria.getRuleId() != null) {
			predicate = ExpressionUtils.and(predicate, rcBlackListReasonHitEntity.ruleId.eq(searchCriteria.getRuleId()));
		}

		if (StringUtils.hasText(searchCriteria.getCardNumber())) {
			predicate = ExpressionUtils.and(predicate, rcBlackListReasonHitEntity.objectId.eq(searchCriteria.getCardNumber()));
		}

		if (StringUtils.hasText(searchCriteria.getObjectId())) {
			predicate = ExpressionUtils.and(predicate, rcBlackListReasonHitEntity.objectId.eq(searchCriteria.getObjectId()));
		}

		if (searchCriteria.getBlackListId() != null) {
			predicate = ExpressionUtils.and(predicate, rcBlackListReasonHitEntity.blackListId.eq(searchCriteria.getBlackListId()));
		}

		return predicate;
	}

	@Override
	@Transactional
	public void addRule(Long ruleId, Long sceneId) {
		log.info("绑定场景规则: ruleId={}, sceneId={}", ruleId, sceneId);

		// 检查规则是否存在
		if (!rcBlackListRuleEntityRepository.existsById(ruleId)) {
			throw new IllegalArgumentException("规则不存在: " + ruleId);
		}

		// 检查场景是否存在
		if (!rcBizScenceEntityRepository.existsById(sceneId)) {
			throw new IllegalArgumentException("场景不存在: " + sceneId);
		}

		// 检查是否已经绑定
		RcScenceRuleEntity existing = rcScenceRuleEntityRepository.findBySceneIdAndRuleId(sceneId, ruleId);
		if (existing != null) {
			throw new IllegalArgumentException("规则已经绑定到该场景");
		}

		// 创建绑定关系
		RcScenceRuleEntity sceneRule = RcScenceRuleEntity.builder()
				.sceneId(sceneId)
				.ruleId(ruleId)
				.createTime(new Date())
				.point(0) // 默认分数为0，可以后续更新
				.build();

		rcScenceRuleEntityRepository.save(sceneRule);
	}

	@Override
	@Transactional
	public void updateSceneRule(SceneRuleInput sceneRuleInput) {
		
		Long ruleId = sceneRuleInput.getRuleId();
		Long sceneId = sceneRuleInput.getSceneId();
		
		log.info("绑定场景规则: ruleId={}, sceneId={}", ruleId, sceneId);

		// 检查规则是否存在
		if (!rcBlackListRuleEntityRepository.existsById(ruleId)) {
			throw new IllegalArgumentException("规则不存在: " + ruleId);
		}

		// 检查场景是否存在
		if (!rcBizScenceEntityRepository.existsById(sceneId)) {
			throw new IllegalArgumentException("场景不存在: " + sceneId);
		}

		// 检查是否已经绑定
		RcScenceRuleEntity existing = rcScenceRuleEntityRepository.findBySceneIdAndRuleId(sceneId, ruleId);
		existing.setPoint(sceneRuleInput.getPoint());
		existing.setUpdateTime(new Date());
		rcScenceRuleEntityRepository.save(existing);

	}

	/**
	 * 构建规则查询条件
	 */
	private com.querydsl.core.types.Predicate buildRuleQueryCondition(String ruleName, Long ruleId, Boolean status) {
		com.querydsl.core.types.Predicate predicate = rcBlackListRuleEntity.isNotNull();

		if (StringUtils.hasText(ruleName)) {
			predicate = com.querydsl.core.types.ExpressionUtils.and(predicate,
					rcBlackListRuleEntity.ruleName.containsIgnoreCase(ruleName));
		}

		if (ruleId != null) {
			predicate = com.querydsl.core.types.ExpressionUtils.and(predicate,
					rcBlackListRuleEntity.id.eq(ruleId));
		}

		if (status != null) {
			predicate = com.querydsl.core.types.ExpressionUtils.and(predicate,
					rcBlackListRuleEntity.status.eq(status));
		}

		return predicate;
	}
}