package com.decathlon.sino.model.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "identity.scheduler")
public class SchedulerProperty {

    private boolean enabled;
    private CronProperty refresh;

    @Setter
    @Getter
    public static class CronProperty {

        private String cron;
        private boolean enabled;
    }

}
