# 风险引擎组件优化说明

## 📋 优化概述

对 `RiskEngineComponent` 进行了全面重构，从原来的评分计算模式改为规则命中检测和记录模式，更符合当前的业务需求。

## 🔄 主要变化

### 1. 业务逻辑调整
- **原有逻辑**: 计算评估分数，进行风险等级评估
- **新逻辑**: 检测规则命中，记录命中详情和原因

### 2. 数据记录策略
- **黑名单记录**: 命中规则时在 `rc_black_list` 表记录
- **命中详情**: 在 `rc_black_list_reason_hit` 表记录具体命中信息
- **上下文保存**: 完整记录命中时的参数上下文

### 3. 参数处理优化
- **预处理机制**: 在表达式执行前完整处理所有参数
- **参数验证**: 确保规则执行前参数完整性
- **上下文记录**: 详细记录命中原因和参数信息

## 🚀 新功能特性

### 1. 规则命中检测
```java
@Service
public class RiskEvaluationService {
    
    @Autowired
    private RiskEngineComponent riskEngineComponent;
    
    public void checkUserRisk(Long sceneId, String userId, Map<String, Object> userInfo) {
        // 评估用户风险规则
        List<RuleHitResult> hitResults = riskEngineComponent.evaluateRules(
            sceneId,           // 场景ID
            userId,            // 用户ID
            userInfo,          // 用户信息上下文
            "system",          // 操作人
            true               // 启用数据库记录
        );
        
        // 处理命中结果
        for (RuleHitResult hit : hitResults) {
            log.warn("用户 {} 命中风险规则: {}, 原因: {}", 
                    userId, hit.getRuleId(), hit.getReason());
            
            // 执行风险处理逻辑
            handleRiskHit(hit);
        }
    }
}
```

### 2. 参数预处理机制
```java
// 上下文参数示例
Map<String, Object> context = new HashMap<>();
context.put("age", 25);
context.put("creditScore", 650);
context.put("orderAmount", new BigDecimal("1000.00"));
context.put("isVip", false);

// 规则参数处理优先级：
// 1. 上下文参数（context中的值）
// 2. 规则默认参数（enableConfig=true的参数）
// 3. 参数缺失时跳过规则评估
```

### 3. 命中记录详情
```java
// RuleHitResult 包含完整的命中信息
RuleHitResult hitResult = {
    "ruleId": 123,
    "objectId": "user_12345",
    "hitTime": "2024-01-15T10:30:00",
    "operator": "system",
    "ruleExpression": "age < 18 && orderAmount > 500",
    "point": 50.0,
    "context": "{\"age\":16,\"orderAmount\":800}",
    "reason": "规则命中: age < 18 && orderAmount > 500, 参数: age=16, orderAmount=800",
    "blackListId": 456,
    "recorded": true
}
```

## 🎯 核心方法说明

### 1. 主要评估方法
```java
/**
 * 评估风险规则命中情况
 * 
 * @param sceneId 场景ID
 * @param objectId 对象ID（如用户ID、订单ID等）
 * @param context 上下文参数
 * @param operator 操作人
 * @param enableRecord 是否启用记录（true=记录到数据库，false=仅检测）
 * @return 规则命中结果列表
 */
public List<RuleHitResult> evaluateRules(Long sceneId, String objectId, 
                                       Map<String, Object> context, 
                                       String operator, Boolean enableRecord)
```

### 2. 参数预处理
- **优先级处理**: 上下文参数 > 默认参数
- **类型安全**: 保持参数原有类型
- **完整性验证**: 确保必需参数都有值

### 3. 表达式执行
- **安全执行**: 异常处理，不影响其他规则
- **结果判断**: 大于0表示命中
- **日志记录**: 详细的执行日志

### 4. 数据记录
- **事务保证**: 使用 `@Transactional` 确保数据一致性
- **黑名单管理**: 自动创建或更新黑名单记录
- **命中详情**: 完整记录命中上下文和原因

## 📊 数据库记录策略

### 1. 黑名单表 (rc_black_list)
```sql
-- 查找或创建黑名单记录
-- 如果存在则更新，不存在则创建新记录
INSERT INTO rc_black_list (object_id, final_score, create_time, update_time)
VALUES ('user_12345', 50.0, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    final_score = final_score + VALUES(final_score),
    update_time = NOW();
```

### 2. 命中记录表 (rc_black_list_reason_hit)
```sql
-- 记录每次规则命中的详细信息
INSERT INTO rc_black_list_reason_hit 
(black_list_id, rule_id, object_id, point, context, create_time, update_time)
VALUES (456, 123, 'user_12345', 50.0, '{"age":16,"orderAmount":800}', NOW(), NOW());
```

## 🔧 配置和使用

### 1. 基本配置
```yaml
# application.yml
logging:
  level:
    com.decathlon.sino.component.RiskEngineComponent: DEBUG
```

### 2. 使用示例
```java
@RestController
public class RiskController {
    
    @Autowired
    private RiskEngineComponent riskEngineComponent;
    
    @PostMapping("/risk/evaluate")
    public ResponseEntity<List<RuleHitResult>> evaluateRisk(
            @RequestParam Long sceneId,
            @RequestParam String objectId,
            @RequestBody Map<String, Object> context) {
        
        List<RuleHitResult> results = riskEngineComponent.evaluateRules(
            sceneId, objectId, context, "api", true
        );
        
        return ResponseEntity.ok(results);
    }
}
```

### 3. 批量评估
```java
public void batchEvaluate(List<String> userIds, Long sceneId) {
    for (String userId : userIds) {
        try {
            Map<String, Object> userContext = getUserContext(userId);
            List<RuleHitResult> hits = riskEngineComponent.evaluateRules(
                sceneId, userId, userContext, "batch", true
            );
            
            if (!hits.isEmpty()) {
                handleUserRiskHits(userId, hits);
            }
        } catch (Exception e) {
            log.error("批量评估用户风险失败: userId={}", userId, e);
        }
    }
}
```

## 🛡️ 错误处理和监控

### 1. 异常处理策略
- **单规则失败**: 不影响其他规则评估
- **参数缺失**: 跳过规则，记录警告日志
- **表达式错误**: 捕获异常，返回未命中
- **数据库错误**: 事务回滚，抛出异常

### 2. 监控指标
```java
// 关键监控指标
- 规则评估总数
- 规则命中率
- 参数缺失率
- 表达式执行失败率
- 数据库记录成功率
```

### 3. 日志级别
- **INFO**: 重要的业务操作（命中、记录）
- **DEBUG**: 详细的执行过程（参数处理、表达式执行）
- **WARN**: 异常情况（参数缺失、规则跳过）
- **ERROR**: 严重错误（表达式失败、数据库错误）

## 🔄 迁移指南

### 从旧版本迁移
1. **方法调用变更**:
   ```java
   // 旧版本
   RiskInfoInput result = riskEngineComponent.evaluate(sceneId, objectId, context, operator, isAudit);
   
   // 新版本
   List<RuleHitResult> results = riskEngineComponent.evaluateRules(sceneId, objectId, context, operator, enableRecord);
   ```

2. **返回值变更**:
   - 旧版本返回评分结果
   - 新版本返回命中规则列表

3. **数据库记录**:
   - 新增黑名单自动管理
   - 新增命中详情记录

## 📈 性能优化

1. **缓存利用**: 充分利用规则缓存组件
2. **批量处理**: 支持批量评估多个对象
3. **异步记录**: 可考虑异步记录数据库（如果性能要求高）
4. **参数复用**: 相同上下文的参数可以复用

## 🎯 最佳实践

1. **参数完整性**: 确保传入完整的上下文参数
2. **错误处理**: 妥善处理规则评估异常
3. **日志监控**: 关注命中率和错误率
4. **定期清理**: 定期清理过期的命中记录
5. **性能监控**: 监控评估性能，优化慢规则

---

**优化完成！** 🎉

新的风险引擎组件提供了更精确的规则命中检测和完整的记录机制。
