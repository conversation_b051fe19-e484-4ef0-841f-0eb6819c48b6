package com.decathlon.sino.service.biz.impl;

import java.time.LocalDateTime;
import org.springframework.stereotype.Service;

import com.decathlon.sino.data.dao.biz.AccountPointEntityRepository;
import com.decathlon.sino.data.entity.biz.AccountPointEntity;
import com.decathlon.sino.service.biz.PointDbService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service(value = "pointDbService")
@AllArgsConstructor
@Slf4j
public class PointDbServiceImpl implements PointDbService {
	
	private final AccountPointEntityRepository accountPointEntityRepository;

	@Override
	public Integer getRangConsumer(String cardNo, LocalDateTime startTime, LocalDateTime endTime) {
		return accountPointEntityRepository.findAllByCardNoAndCreatedAtBetween(cardNo, startTime, endTime)
				.stream()
				.filter(accountPointEntity -> accountPointEntity.getPointChange() < 0)
				.map(AccountPointEntity::getPointChange)
				.reduce(0, Integer::sum);
	}

	@Override
	public Integer getRangProduce(String cardNo, LocalDateTime startTime, LocalDateTime endTime) {
		return accountPointEntityRepository.findAllByCardNoAndCreatedAtBetween(cardNo, startTime, endTime)
				.stream()
				.filter(accountPointEntity -> accountPointEntity.getPointChange() > 0)
				.map(AccountPointEntity::getPointChange)
				.reduce(0, Integer::sum);
	}

	@Override
	public Integer getBlanceAfterUse(String cardNo, Integer pointChange) {
		AccountPointEntity accountPointEntity =  accountPointEntityRepository.getMaxIdByCardNo(cardNo);
		if(accountPointEntity != null) {
			Integer pointBalance = accountPointEntity.getPointBalance();
			if(pointBalance != null) {
				return pointBalance + pointChange;
			}
		}
		return null;
	}

	@Override
	public void save(AccountPointEntity accountPointEntity) {
		log.info("Saving account point entity: {}", accountPointEntity);
		accountPointEntityRepository.findByCardNoAndEventId(accountPointEntity.getCardNo(), accountPointEntity.getEventId())
				.ifPresent(existingEntity -> {
					accountPointEntity.setId(existingEntity.getId());
					accountPointEntity.setCreatedAt(existingEntity.getCreatedAt());
				});
		accountPointEntityRepository.save(accountPointEntity);
	}

	@Override
	public AccountPointEntity getMinByCardNumber(String cardNumber) {
		return accountPointEntityRepository.getMinByCardNumber(cardNumber);
	}

	@Override
	public AccountPointEntity getLastByCardNumber(String cardNumber) {
		return accountPointEntityRepository.getLastByCardNumber(cardNumber);
	}

	@Override
	public AccountPointEntity checkAndSave(AccountPointEntity accountPointEntity) {
		accountPointEntityRepository.findByCardNoAndEventId(accountPointEntity.getCardNo(), accountPointEntity.getEventId())
				.ifPresent(existingEntity -> {
					accountPointEntity.setId(existingEntity.getId());
					accountPointEntity.setUpdatedAt(LocalDateTime.now());
				});
		return accountPointEntityRepository.save(accountPointEntity);
	}

	

}
