# 增强风险查询优先级和拦截状态

## 📋 增强概述

根据业务需求，对风险查询服务进行了全面增强，实现了完整的优先级查询链和拦截状态管理。

## 🎯 查询优先级链

### 完整的查询顺序

```
1. 全局黑白名单 (rc_blacklist_global)
   ├── 全局黑名单 → 高风险 + 拦截
   └── 全局白名单 → 低风险 + 不拦截

2. 全局场景黑名单 (rc_global_scene_blacklist)
   └── 全局场景黑名单 → 高风险 + 拦截

3. 场景黑名单 (rc_black_list)
   └── 场景黑名单 → 根据场景配置决定是否拦截

4. 无风险状态
   └── 不在任何名单中 → 低风险 + 不拦截
```

### 拦截状态逻辑

| 名单类型 | 拦截状态 | 说明 |
|---------|---------|------|
| **全局黑名单** | ✅ 拦截 | 默认拦截，最高优先级 |
| **全局白名单** | ❌ 不拦截 | 绝对安全，跳过所有检查 |
| **全局场景黑名单** | ✅ 拦截 | 特定场景的全局拦截 |
| **场景黑名单** | 🔄 根据场景配置 | 依据 `rc_biz_scence.is_block` 字段 |
| **无风险** | ❌ 不拦截 | 默认放行 |

## 🚀 核心增强功能

### 1. 直接查询模式增强

```java
public RiskQueryResult checkRisk(Long sceneId, String objectId) {
    // 1. 优先查询全局黑白名单
    RcBlacklistGlobalEntity globalEntity = rcGlobalBlackListEntityRepository.findByObjectId(objectId);
    if (globalEntity != null) {
        return buildGlobalRiskResult(sceneId, objectId, globalEntity);
    }
    
    // 2. 查询全局场景黑名单
    RcGlobalSceneBlacklistEntity globalSceneEntity = rcGlobalSceneBlacklistEntityRepository
            .findBySceneIdAndObjectId(sceneId, objectId);
    if (globalSceneEntity != null) {
        return buildGlobalSceneRiskResult(sceneId, objectId, globalSceneEntity);
    }
    
    // 3. 查询场景级别的黑名单记录
    RcBlackListEntity blackListEntity = rcBlackListEntityRepository.findBySceneIdAndObjectId(sceneId, objectId);
    if (blackListEntity == null) {
        return buildNoRiskResult(sceneId, objectId);
    }
    
    // 4. 查询场景拦截状态
    RcBizScenceEntity sceneEntity = rcBizScenceEntityRepository.findById(sceneId).orElse(null);
    
    // 5. 构建风险查询结果（包含拦截状态）
    return buildRiskQueryResult(blackListEntity, hitRecords, sceneEntity);
}
```

### 2. 审计评估模式增强

```java
public RiskEvaluationResult auditRisk(Long sceneId, String objectId, Map<String, Object> context, 
        String operator, Boolean enableRecord) {
    // 1. 优先检查全局黑白名单
    RcBlacklistGlobalEntity globalEntity = rcGlobalBlackListEntityRepository.findByObjectId(objectId);
    if (globalEntity != null) {
        return buildGlobalEvaluationResult(sceneId, objectId, globalEntity);
    }
    
    // 2. 检查全局场景黑名单
    RcGlobalSceneBlacklistEntity globalSceneEntity = rcGlobalSceneBlacklistEntityRepository
            .findBySceneIdAndObjectId(sceneId, objectId);
    if (globalSceneEntity != null) {
        return buildGlobalSceneEvaluationResult(sceneId, objectId, globalSceneEntity);
    }
    
    // 3. 执行规则评估
    List<RuleHitResult> hitResults = riskEngineComponent.evaluateRules(sceneId, objectId, context, operator, enableRecord);
    
    // 4. 查询场景拦截状态
    RcBizScenceEntity sceneEntity = rcBizScenceEntityRepository.findById(sceneId).orElse(null);
    
    // 5. 构建评估结果（包含拦截状态）
    return buildEvaluationResult(sceneId, objectId, hitResults, blackListEntity, sceneEntity, enableRecord);
}
```

## 🛡️ 拦截状态管理

### 1. 全局名单拦截逻辑

```java
// 全局黑名单 - 强制拦截
if ("BLACKLIST".equals(globalEntity.getType())) {
    result.setBlocked(true);  // 强制拦截
    result.setRiskLevel("HIGH");
    result.setTotalScore(new BigDecimal("999"));
}

// 全局白名单 - 强制放行
else if ("WHITELIST".equals(globalEntity.getType())) {
    result.setBlocked(false); // 强制放行
    result.setRiskLevel("LOW");
    result.setTotalScore(BigDecimal.ZERO);
}
```

### 2. 场景拦截逻辑

```java
// 场景黑名单 - 根据场景配置决定
if (sceneEntity != null && sceneEntity.getIsBlock() != null) {
    // 场景配置了拦截 && (在黑名单中 || 有新命中)
    result.setBlocked(sceneEntity.getIsBlock() && (result.getInBlacklist() || result.getHasNewHits()));
} else {
    result.setBlocked(false); // 默认不拦截
}
```

### 3. 拦截状态响应

```json
{
  "sceneId": 1,
  "objectId": "user_12345",
  "inBlacklist": true,
  "riskLevel": "HIGH",
  "totalScore": 85.5,
  "blocked": true,  // 新增拦截状态字段
  "hitRuleCount": 3,
  "lastHitTime": "2024-01-15T10:30:00",
  "queryTime": "2024-01-15T14:20:00"
}
```

## 📊 业务场景示例

### 1. VIP用户（全局白名单）

```java
// VIP用户在全局白名单中
RiskQueryResult result = auditProcessService.checkRisk(PAYMENT_SCENE_ID, "vip_user_123");

// 结果：
// inBlacklist: false
// riskLevel: "LOW"
// totalScore: 0
// blocked: false  ← 绝对不拦截
// 说明：即使支付金额很大，也不会被拦截
```

### 2. 欺诈用户（全局黑名单）

```java
// 欺诈用户在全局黑名单中
RiskQueryResult result = auditProcessService.checkRisk(LOGIN_SCENE_ID, "fraud_user_456");

// 结果：
// inBlacklist: true
// riskLevel: "HIGH"
// totalScore: 999
// blocked: true   ← 强制拦截
// 说明：无论场景配置如何，都会被拦截
```

### 3. 特定场景风险用户（全局场景黑名单）

```java
// 用户在支付场景的全局黑名单中
RiskQueryResult result = auditProcessService.checkRisk(PAYMENT_SCENE_ID, "risky_user_789");

// 结果：
// inBlacklist: true
// riskLevel: "HIGH"
// totalScore: 888
// blocked: true   ← 该场景强制拦截
// 说明：只在支付场景被拦截，其他场景正常
```

### 4. 普通风险用户（场景黑名单）

```java
// 用户在场景黑名单中，场景配置了拦截
RiskQueryResult result = auditProcessService.checkRisk(REGISTRATION_SCENE_ID, "normal_user_101");

// 结果：
// inBlacklist: true
// riskLevel: "MEDIUM"
// totalScore: 45.0
// blocked: true/false  ← 根据场景的 is_block 配置决定
// 说明：拦截状态取决于场景配置
```

## 🔧 数据库表结构

### 1. 全局黑白名单表

```sql
-- rc_blacklist_global
CREATE TABLE rc_blacklist_global (
    id BIGINT PRIMARY KEY,
    object_id VARCHAR(255) NOT NULL,
    type VARCHAR(20) NOT NULL,  -- 'BLACKLIST' 或 'WHITELIST'
    create_time TIMESTAMP,
    UNIQUE KEY uk_object_id (object_id)
);
```

### 2. 全局场景黑名单表

```sql
-- rc_global_scene_blacklist
CREATE TABLE rc_global_scene_blacklist (
    id BIGINT PRIMARY KEY,
    scene_id BIGINT NOT NULL,
    object_id VARCHAR(255) NOT NULL,
    create_time TIMESTAMP,
    UNIQUE KEY uk_scene_object (scene_id, object_id)
);
```

### 3. 场景配置表

```sql
-- rc_biz_scence
CREATE TABLE rc_biz_scence (
    id BIGINT PRIMARY KEY,
    scene_name VARCHAR(255),
    is_block BOOLEAN,  -- 是否拦截
    create_time TIMESTAMP
);
```

## 🎯 最佳实践

### 1. 优先级管理

- **全局白名单**: 用于VIP用户、内部测试账号等绝对安全的用户
- **全局黑名单**: 用于已确认的欺诈用户、恶意用户等
- **全局场景黑名单**: 用于特定场景的高风险用户
- **场景黑名单**: 用于常规的风险控制

### 2. 拦截策略

- **强制拦截**: 全局黑名单、全局场景黑名单
- **配置拦截**: 场景黑名单根据业务需要配置
- **绝对放行**: 全局白名单用户

### 3. 监控告警

- **拦截率监控**: 各层级的拦截率统计
- **误拦告警**: 全局白名单用户被其他规则命中的告警
- **拦截效果**: 拦截后的业务指标变化监控

---

**增强完成！** 🎉

现在的风险查询服务提供了完整的优先级查询链和智能的拦截状态管理，确保了业务逻辑的正确性和灵活性。
