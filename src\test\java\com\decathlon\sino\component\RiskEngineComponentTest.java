package com.decathlon.sino.component;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.decathlon.sino.App;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringJUnit4ClassRunner.class)
@EnableAutoConfiguration
@SpringBootTest(classes = App.class)
@Slf4j
class RiskEngineComponentTest {
	
	@Autowired
	private RiskEngineComponent riskEngineComponent;
	
	
	@Test
	void test() {
		
		HashMap<String, Object> context = new HashMap<>();
		
		context.put("store", "JD");
		
		riskEngineComponent.evaluateRules(4L,"wang", context, "sys", true);
		
		fail("Not yet implemented");
	}

}
