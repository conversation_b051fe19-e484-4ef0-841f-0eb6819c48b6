package com.decathlon.sino.controller;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import com.decathlon.sino.common.annotations.StartSwaggerScan;
import com.decathlon.sino.common.config.PageResultDTO;
import com.decathlon.sino.model.criteria.BlackListSearchCriteria;
import com.decathlon.sino.model.criteria.HitRecordSearchCriteria;
import com.decathlon.sino.model.dto.AuditRuleBaseInfoDto;
import com.decathlon.sino.model.input.AuditSceneInput;
import com.decathlon.sino.model.input.RuleInput;
import com.decathlon.sino.model.input.RuleParamInput;
import com.decathlon.sino.model.input.SceneRuleInput;
import com.decathlon.sino.model.ouput.AuditRuleDetailOutput;
import com.decathlon.sino.model.ouput.AuditSceneDetailOutput;
import com.decathlon.sino.model.ouput.AuditSceneOutput;
import com.decathlon.sino.model.ouput.RuleBlackListOutput;
import com.decathlon.sino.model.ouput.RuleHitRecordOutput;
import com.decathlon.sino.service.AuditRuleManagerService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@StartSwaggerScan
@RestController
@Slf4j
@AllArgsConstructor
public class AuditRuleBackOfficeController {
	
    private final AuditRuleManagerService auditRuleManagerService;
    

    /**
     * 创建一个新的审计场景
     * 返回 场景ID,biz_type
     * @param auditSceneInput
     */
    @PostMapping("/scene")
    public AuditSceneOutput createAuditScene(@RequestBody AuditSceneInput auditSceneInput) {
      return   auditRuleManagerService.createAuditScene(auditSceneInput);
    }

    /**
	 * 查询所有审计场景
	 */
    @GetMapping("/scene")
    public List<AuditSceneOutput> getScences() {
        return auditRuleManagerService.getScences();
    }
    
    /**
	 * 根据场景ID获取审计场景详情
	 * @param sceneId
	 */
    @GetMapping("/scene/detail")
    public AuditSceneDetailOutput getSceneDetail(@RequestParam(value = "scene_id") Long sceneId) {
        return auditRuleManagerService.getSceneDetail(sceneId);
    }
    
    /**
	 * 修改一个现有的审计场景
	 * @param auditSceneInput
	 */
    @PutMapping("/scene")
    public void updateRule(@RequestBody AuditSceneInput auditSceneInput) {
        auditRuleManagerService.updateAuditScene(auditSceneInput);
    }
    
    /**
	 * 获取所有规则参数
	 * @return List of RuleParamInput
	 */
    @GetMapping("/params")
    public List<RuleParamInput> getAllRuleParams() {
        return auditRuleManagerService.getAllRuleParams();
    }
    
    /**
     * 查询所有审计规则
     * @param ruleName
     * @param ruleId
     * @param status
     */
    @GetMapping("/rule")
    public List<AuditRuleBaseInfoDto> getRules(@RequestParam(value = "rule_name", required = false) String ruleName,
                                               @RequestParam(value = "rule_id", required = false) Long ruleId,
                                               @RequestParam(value = "status",required = false ) Boolean status
    ) {
         return auditRuleManagerService.getRules(ruleName, ruleId,status);
    }
    
    /**
	 * 根据业务类型ID获取规则
	 * @param ruleId
	 */
    @GetMapping("/rule/detail")
    public AuditRuleDetailOutput getRuleDetail(@RequestParam(value = "rule_id") Long ruleId) {
         return auditRuleManagerService.getRuleDetail(ruleId);
    }
    
    /**
	 * 创建一个新的审计规则
	 * @return
	 */
    @PostMapping("/rule")
    public void creatRule(@RequestBody RuleInput ruleInput) {
        auditRuleManagerService.createRule(ruleInput);
    }
    
    
    /**
	 * 更新一个现有的审计规则
	 * @param ruleInput
	 */
    @PutMapping("/rule")
    public void updateRule(@RequestBody RuleInput ruleInput) {
        auditRuleManagerService.updateRule(ruleInput);
    }
    
    /**
     * 解绑规则
     * @param ruleId 规则表ID
     */
    @DeleteMapping("/scene/rule")
    public void updateRule(@RequestParam(value = "rule_id") Long ruleId,@RequestParam(value = "scene_id") Long sceneId) {
        auditRuleManagerService.deleteRule(ruleId,sceneId);
    }
    
    /**
     * 绑定场景规则
     * @param ruleId 规则表ID
     */
    @PostMapping("/scene/rule")
    public void addRule(@RequestParam(value = "rule_id") Long ruleId,@RequestParam(value = "scene_id") Long sceneId) {
        auditRuleManagerService.addRule(ruleId, sceneId);
    }
    
    /**
     * 修改场景规则积分
     * @param sceneRuleInput
     */
    @PutMapping("/scene/rule")
    public void updateSceneRule(@RequestBody SceneRuleInput sceneRuleInput) {
        auditRuleManagerService.updateSceneRule(sceneRuleInput);
    }
    
    
    
    /**
	 * 根据业务类型ID获取黑名单列表
	 * @param sceneId
	 * @param objectId
	 * @return
	 */
    @GetMapping("/blacklist")
    public PageResultDTO<RuleBlackListOutput> getRuleBlackList(@RequestParam(value = "scene_id") Long sceneId,@RequestParam(value = "card_number",required = false) String objectId, BlackListSearchCriteria searchCriteria) {
    	searchCriteria.setSceneId(sceneId);
    	if(StringUtils.isNotBlank(objectId)) {
			searchCriteria.setObjectId(objectId);
		}
    	return auditRuleManagerService.getRuleBlackList(searchCriteria);
    }
    
    
    /**
     * 根据黑名单ID获取规则命中记录详情
     * @param blackId
     * @return
     */
    @GetMapping("/hitRecords")
    public List<RuleHitRecordOutput> getRuleHitRecords(@RequestParam(value = "black_id") Long blackId) {
    	return auditRuleManagerService.getRuleHitRecords(blackId);
    }
    
    /**
     * 根据规则ID获取规则命中记录详情
     * @param ruleId
     * @param objectId
     */
    @GetMapping("/rule/hitRecords")
    public PageResultDTO<RuleHitRecordOutput> getRuleHitRecordsByType(@RequestParam(value = "rule_id") Long ruleId,@RequestParam(value = "card_number",required = false) String objectId, HitRecordSearchCriteria searchCriteria) {
    	searchCriteria.setRuleId(ruleId);
    	if(StringUtils.isNotBlank(objectId)) {
			searchCriteria.setObjectId(objectId);
		}
    	return auditRuleManagerService.getRuleHitRecordsByRuleId(searchCriteria);
    }
    

}
