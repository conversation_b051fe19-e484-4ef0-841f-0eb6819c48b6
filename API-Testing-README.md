# 审计规则管理系统 API 测试指南

本目录包含了审计规则管理系统所有接口的测试工具和文档。

## 文件说明

### 1. `api-curl-commands.sh` - 交互式测试脚本
功能丰富的 Bash 脚本，提供了所有接口的封装函数。

**特性:**
- 🎨 彩色输出，易于阅读
- 🔧 环境检查和配置
- 📝 详细的帮助信息
- 🚀 一键执行完整测试流程
- 🔄 支持多环境切换

**使用方法:**
```bash
# 1. 给脚本执行权限
chmod +x api-curl-commands.sh

# 2. 加载脚本函数
source api-curl-commands.sh

# 3. 查看帮助
show_help

# 4. 检查环境
check_environment

# 5. 执行单个测试
create_scene
get_all_scenes
get_scene_detail 1

# 6. 执行完整测试流程
run_full_test
```

### 2. `api-curl-simple.txt` - 原始 curl 命令集合
包含所有接口的原始 curl 命令，便于复制粘贴。

**使用方法:**
- 直接复制需要的 curl 命令
- 根据实际环境修改 URL
- 在终端中执行

## 接口分类

### 🏢 场景管理接口
- `POST /scene` - 创建审计场景
- `GET /scene` - 查询所有审计场景
- `GET /scene/detail` - 获取场景详情
- `PUT /scene` - 更新审计场景

### ⚙️ 规则参数接口
- `GET /params` - 获取所有规则参数

### 📋 规则管理接口
- `GET /rule` - 查询规则列表
- `GET /rule/detail` - 获取规则详情
- `POST /rule` - 创建规则
- `PUT /rule` - 更新规则

### 🔗 场景规则关联接口
- `POST /scene/rule` - 绑定场景规则
- `DELETE /scene/rule` - 解绑规则
- `PUT /scence/rule` - 更新场景规则

### 📊 黑名单和命中记录接口
- `GET /blacklist` - 获取黑名单列表
- `GET /hitRecords` - 获取规则命中记录（按黑名单ID）
- `GET /rule/hitRecords` - 获取规则命中记录（按规则ID）

## 快速开始

### 1. 环境准备
```bash
# 安装必要工具
sudo apt-get install curl jq  # Ubuntu/Debian
brew install curl jq          # macOS

# 确保服务运行
curl http://localhost:8080/scene
```

### 2. 基础测试流程
```bash
# 加载测试脚本
source api-curl-commands.sh

# 执行完整测试
run_full_test
```

### 3. 自定义测试
```bash
# 设置环境
set_env "http://your-server:8080"

# 创建场景
create_scene

# 创建规则
create_rule

# 绑定规则到场景
bind_scene_rule 1 1

# 查询结果
get_scene_detail 1
get_rule_detail 1
```

## 常用命令示例

### 创建完整的测试数据
```bash
# 1. 创建场景
curl -X POST "http://localhost:8080/scene" \
  -H "Content-Type: application/json" \
  -d '{
    "classification": "风险控制",
    "scene": "用户注册",
    "description": "用户注册场景风险评估",
    "intercept": true,
    "status": true,
    "createBy": "admin"
  }'

# 2. 创建规则
curl -X POST "http://localhost:8080/rule" \
  -H "Content-Type: application/json" \
  -d '{
    "ruleName": "身份证号码验证",
    "triggerType": "BEFORE_SAVE",
    "expression": "idCard.length() == 18",
    "description": "验证身份证号码长度",
    "status": true,
    "createBy": "admin"
  }'

# 3. 绑定规则到场景
curl -X POST "http://localhost:8080/scene/rule?rule_id=1&scene_id=1"
```

### 查询和验证
```bash
# 查询场景详情
curl -X GET "http://localhost:8080/scene/detail?scene_id=1"

# 查询规则详情
curl -X GET "http://localhost:8080/rule/detail?rule_id=1"

# 查询黑名单
curl -X GET "http://localhost:8080/blacklist?scene_id=1"

# 查询命中记录
curl -X GET "http://localhost:8080/rule/hitRecords?rule_id=1"
```

## 高级功能

### 环境切换
```bash
# 切换到开发环境
set_env dev

# 切换到测试环境
set_env test

# 切换到自定义环境
set_env "http://custom-server:8080"
```

### 批量测试
```bash
# 测试所有场景接口
create_scene
get_all_scenes
get_scene_detail 1
update_scene

# 测试所有规则接口
get_all_params
create_rule
get_rules
get_rule_detail 1
update_rule
```

### 错误调试
```bash
# 添加详细输出
curl -v -X GET "http://localhost:8080/scene"

# 只查看HTTP状态码
curl -s -o /dev/null -w "%{http_code}" "http://localhost:8080/scene"

# 测试连接
test_connection
```

## 注意事项

1. **服务地址**: 默认使用 `http://localhost:8080`，请根据实际情况修改
2. **认证**: 如果系统启用了认证，需要添加相应的认证头部
3. **数据格式**: POST/PUT 请求使用 JSON 格式
4. **分页**: 支持标准的 Spring Data 分页参数 (`page`, `size`, `sort`)
5. **错误处理**: 建议使用 `-v` 参数查看详细的请求响应信息

## 故障排除

### 常见问题

1. **连接被拒绝**
   ```bash
   # 检查服务是否运行
   curl http://localhost:8080/actuator/health
   ```

2. **JSON 格式错误**
   ```bash
   # 使用 jq 验证 JSON 格式
   echo '{"test": "data"}' | jq .
   ```

3. **权限问题**
   ```bash
   # 给脚本执行权限
   chmod +x api-curl-commands.sh
   ```

### 获取帮助
```bash
# 查看脚本帮助
source api-curl-commands.sh
show_help

# 查看原始 curl 命令
show_raw_curls

# 检查环境
check_environment
```

## 贡献

如果您发现问题或有改进建议，请：
1. 检查现有的接口文档
2. 验证 curl 命令的正确性
3. 提供详细的错误信息和复现步骤

---

**Happy Testing! 🚀**
