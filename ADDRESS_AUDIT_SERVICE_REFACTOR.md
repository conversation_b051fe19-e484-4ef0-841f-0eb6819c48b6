# AddressAuditServiceImpl 重构说明

## 📋 重构概述

对 `AddressAuditServiceImpl` 进行了全面重构，提升了代码的可读性、可维护性和功能完整性。

## 🔄 主要改进

### 1. 代码结构优化

**重构前**:
```java
@Service
public class AddressAuditServiceImpl extends AduitProcessService implements AuditService {
    
    public AddressAuditServiceImpl(RiskEngineComponent riskEngineComponent,...) {
        super(riskEngineComponent, ...);
    }

    @Override
    public RiskQueryResult checkRisk(Long bizType, String obejctId,JSONObject eventData,String operator, Boolean isAudit) {
        if(Boolean.TRUE.equals(isAudit)) {
            return this.auditRisk(bizType, obejctId, eventData, operator, isAudit);
        }else {
            return super.checkRisk(bizType, obejctId);
        }
    }

    @Override
    public RiskEvaluationResult auditRisk(Long bizType, String obejctId, JSONObject eventData,String operator,Boolean isAudit) {
        Map<String,Object> ctx = AddressRiskHelper.getContext(eventData);
        return super.auditRisk(bizType, obejctId,ctx, operator,isAudit);
    }
}
```

**重构后**:
```java
/**
 * 地址审计服务实现
 * 
 * 专门处理地址相关的风险审计，包括：
 * - 收货地址风险评估
 * - 地址异常检测
 * - 地址黑名单管理
 */
@Service
@Slf4j
public class AddressAuditServiceImpl extends AduitProcessService implements AuditService {
    
    // 完整的构造函数注入
    // 详细的方法实现
    // 异常处理和日志记录
    // 辅助方法提取
}
```

### 2. 方法功能增强

#### checkRisk 方法增强
```java
@Override
public RiskQueryResult checkRisk(Long sceneId, String objectId, JSONObject eventData, String operator, Boolean enableAudit) {
    log.info("开始地址风险检查: sceneId={}, objectId={}, enableAudit={}", sceneId, objectId, enableAudit);
    
    try {
        if (Boolean.TRUE.equals(enableAudit)) {
            // 审计模式：先评估再返回结果
            log.debug("使用审计模式进行地址风险评估");
            RiskEvaluationResult evaluationResult = this.auditRisk(sceneId, objectId, eventData, operator, enableAudit);
            
            // 将评估结果转换为查询结果格式
            return convertEvaluationToQueryResult(evaluationResult);
        } else {
            // 直接查询模式：快速查询现有风险状态
            log.debug("使用直接查询模式检查地址风险");
            return super.checkRisk(sceneId, objectId);
        }
    } catch (Exception e) {
        log.error("地址风险检查失败: sceneId={}, objectId={}", sceneId, objectId, e);
        throw new RuntimeException("地址风险检查失败", e);
    }
}
```

#### auditRisk 方法增强
```java
@Override
public RiskEvaluationResult auditRisk(Long sceneId, String objectId, JSONObject eventData, String operator, Boolean enableRecord) {
    log.info("开始地址风险审计评估: sceneId={}, objectId={}, enableRecord={}", sceneId, objectId, enableRecord);
    
    try {
        // 1. 提取地址相关的上下文信息
        Map<String, Object> addressContext = extractAddressContext(eventData);
        log.debug("地址上下文提取完成: {}", addressContext);
        
        // 2. 调用父类的审计评估方法
        RiskEvaluationResult result = super.auditRisk(sceneId, objectId, addressContext, operator, enableRecord);
        
        // 3. 增强地址特定的风险信息
        enhanceAddressRiskInfo(result, eventData);
        
        log.info("地址风险审计评估完成: sceneId={}, objectId={}, hitCount={}", 
                sceneId, objectId, result.getHitRuleCount());
        
        return result;
    } catch (Exception e) {
        log.error("地址风险审计评估失败: sceneId={}, objectId={}", sceneId, objectId, e);
        throw new RuntimeException("地址风险审计评估失败", e);
    }
}
```

### 3. 新增辅助方法

#### 地址上下文提取
```java
/**
 * 提取地址相关的上下文信息
 */
private Map<String, Object> extractAddressContext(JSONObject eventData) {
    if (eventData == null) {
        log.warn("事件数据为空，使用空的地址上下文");
        return Map.of();
    }
    
    try {
        // 使用 AddressRiskHelper 提取地址上下文
        Map<String, Object> context = AddressRiskHelper.getContext(eventData);
        log.debug("地址上下文提取成功: keys={}", context.keySet());
        return context;
    } catch (Exception e) {
        log.error("地址上下文提取失败", e);
        return Map.of();
    }
}
```

#### 地址风险信息增强
```java
/**
 * 增强地址特定的风险信息
 */
private void enhanceAddressRiskInfo(RiskEvaluationResult result, JSONObject eventData) {
    // 这里可以添加地址特定的风险信息增强逻辑
    // 例如：地址风险等级、地址类型、地理位置风险等
    
    // 示例：添加地址类型信息到结果中
    if (eventData != null && eventData.containsKey("addressType")) {
        // 可以在结果中添加额外的地址风险信息
        log.debug("地址类型: {}", eventData.getStr("addressType"));
    }
}
```

#### 结果格式转换
```java
/**
 * 将评估结果转换为查询结果格式
 */
private RiskQueryResult convertEvaluationToQueryResult(RiskEvaluationResult evaluationResult) {
    RiskQueryResult queryResult = new RiskQueryResult();
    queryResult.setSceneId(evaluationResult.getSceneId());
    queryResult.setObjectId(evaluationResult.getObjectId());
    queryResult.setInBlacklist(evaluationResult.getInBlacklist());
    queryResult.setRiskLevel(evaluationResult.getCurrentRiskLevel());
    queryResult.setTotalScore(evaluationResult.getCurrentTotalScore());
    queryResult.setHitRuleCount(evaluationResult.getHitRuleCount());
    queryResult.setBlocked(evaluationResult.getBlocked());
    queryResult.setQueryTime(evaluationResult.getEvaluationTime());
    
    // 设置命中记录为空（审计模式下的新命中记录不包含在查询结果中）
    queryResult.setHitRecords(java.util.Collections.emptyList());
    queryResult.setLastHitTime(null);
    
    return queryResult;
}
```

## 🚀 功能特性

### 1. 完整的日志记录
- **INFO级别**: 记录关键业务操作
- **DEBUG级别**: 记录详细的执行过程
- **ERROR级别**: 记录异常和错误信息

### 2. 异常处理
- 统一的异常捕获和处理
- 详细的错误日志记录
- 业务异常的包装和抛出

### 3. 参数验证
- 空值检查和默认值处理
- 数据完整性验证
- 容错处理机制

### 4. 扩展性设计
- 预留地址特定的风险信息增强接口
- 支持未来的地址风险规则扩展
- 灵活的上下文信息提取机制

## 📊 使用示例

### 1. 直接查询模式
```java
@RestController
public class AddressController {
    
    @Autowired
    private AddressAuditServiceImpl addressAuditService;
    
    @GetMapping("/address/risk/check")
    public ResponseEntity<RiskQueryResult> checkAddressRisk(
            @RequestParam Long sceneId,
            @RequestParam String userId,
            @RequestBody JSONObject addressData) {
        
        RiskQueryResult result = addressAuditService.checkRisk(
            sceneId, userId, addressData, "api", false
        );
        
        return ResponseEntity.ok(result);
    }
}
```

### 2. 审计评估模式
```java
@PostMapping("/address/risk/audit")
public ResponseEntity<RiskEvaluationResult> auditAddressRisk(
        @RequestParam Long sceneId,
        @RequestParam String userId,
        @RequestBody JSONObject addressData,
        @RequestParam(defaultValue = "api") String operator,
        @RequestParam(defaultValue = "true") Boolean enableRecord) {
    
    RiskEvaluationResult result = addressAuditService.auditRisk(
        sceneId, userId, addressData, operator, enableRecord
    );
    
    return ResponseEntity.ok(result);
}
```

### 3. 地址数据格式示例
```json
{
  "addressType": "DELIVERY",
  "province": "广东省",
  "city": "深圳市",
  "district": "南山区",
  "street": "科技园南区",
  "detailAddress": "腾讯大厦",
  "postalCode": "518000",
  "receiverName": "张三",
  "receiverPhone": "13800138000",
  "coordinates": {
    "latitude": 22.5431,
    "longitude": 113.9344
  }
}
```

## 🎯 最佳实践

### 1. 错误处理
- 使用统一的异常处理机制
- 记录详细的错误上下文信息
- 提供有意义的错误消息

### 2. 性能优化
- 合理使用缓存机制
- 避免重复的数据库查询
- 优化地址上下文提取逻辑

### 3. 监控和告警
- 监控地址风险检查的成功率
- 统计地址风险的命中率
- 设置异常情况的告警

### 4. 扩展开发
- 在 `enhanceAddressRiskInfo` 方法中添加地址特定逻辑
- 扩展 `AddressRiskHelper` 的上下文提取能力
- 增加地址风险规则的配置管理

---

**重构完成！** 🎉

重构后的 `AddressAuditServiceImpl` 提供了更清晰的代码结构、完善的异常处理、详细的日志记录和良好的扩展性。
