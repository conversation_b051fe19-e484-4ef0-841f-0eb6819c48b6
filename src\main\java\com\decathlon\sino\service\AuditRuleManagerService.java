package com.decathlon.sino.service;

import com.decathlon.sino.common.config.PageResultDTO;
import com.decathlon.sino.model.criteria.BlackListSearchCriteria;
import com.decathlon.sino.model.criteria.HitRecordSearchCriteria;
import com.decathlon.sino.model.dto.AuditRuleBaseInfoDto;
import com.decathlon.sino.model.input.*;
import com.decathlon.sino.model.ouput.AuditRuleDetailOutput;
import com.decathlon.sino.model.ouput.AuditSceneDetailOutput;
import com.decathlon.sino.model.ouput.AuditSceneOutput;
import com.decathlon.sino.model.ouput.RuleBlackListOutput;
import com.decathlon.sino.model.ouput.RuleHitRecordOutput;

import java.util.List;

/**
 * 系统参数管理服务接口
 * <AUTHOR>
 */
public interface AuditRuleManagerService {
	
	AuditSceneOutput createAuditScene(AuditSceneInput auditSceneInput);

	void updateAuditScene(AuditSceneInput auditSceneInput);

	PageResultDTO<RuleBlackListOutput> getRuleBlackList(BlackListSearchCriteria searchCriteria);

	PageResultDTO<RuleHitRecordOutput> getRuleHitRecordsByRuleId(HitRecordSearchCriteria searchCriteria);

	List<AuditRuleBaseInfoDto> getRules(String ruleName,Long ruleId,Boolean status);

	List<RuleParamInput> getAllRuleParams();

	void createRule(RuleInput ruleInput);

	void updateRule(RuleInput ruleInput);

	List<RuleHitRecordOutput> getRuleHitRecords(Long blackId);

	void deleteRule(Long ruleId,Long sceneId);

	AuditSceneDetailOutput getSceneDetail(Long sceneId);

	AuditRuleDetailOutput getRuleDetail(Long ruleId);

	List<AuditSceneOutput> getScences();

	void addRule(Long ruleId, Long sceneId);

	void updateSceneRule(SceneRuleInput sceneRuleInput);

}
