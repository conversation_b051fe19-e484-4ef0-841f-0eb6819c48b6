package com.decathlon.sino.model.ouput;

import java.math.BigDecimal;
import java.util.Date;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RuleBlackListOutput {
	
	private Long blackListId;
	private Date createTime;
	private String objectId;
	private BigDecimal totalScore;

}
