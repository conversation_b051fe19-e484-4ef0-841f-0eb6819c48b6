package com.decathlon.sino.model.criteria;

import com.decathlon.sino.data.entity.QRcBlackListEntity;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class BlackListSearchCriteria extends SearchCriteria implements Criteria {
	private static final long serialVersionUID = 1L;

	private Long sceneId;
	private String cardNumber;
	private String objectId;
	private Boolean status;
	private Integer riskLevel;

	@Override
	public Predicate toPredicate() {
		QRcBlackListEntity blackList = QRcBlackListEntity.rcBlackListEntity;
		Predicate predicate = blackList.isNotNull();

		if (sceneId != null) {
			predicate = ExpressionUtils.and(predicate, blackList.sceneId.eq(sceneId));
		}

		if (StringUtils.isNotBlank(cardNumber)) {
			predicate = ExpressionUtils.and(predicate, blackList.objectId.eq(cardNumber));
		}

		if (StringUtils.isNotBlank(objectId)) {
			predicate = ExpressionUtils.and(predicate, blackList.objectId.eq(objectId));
		}

		if (status != null) {
			predicate = ExpressionUtils.and(predicate, blackList.status.eq(status));
		}

		if (riskLevel != null) {
			predicate = ExpressionUtils.and(predicate, blackList.riskLevel.eq(riskLevel));
		}

		return predicate;
	}
}
