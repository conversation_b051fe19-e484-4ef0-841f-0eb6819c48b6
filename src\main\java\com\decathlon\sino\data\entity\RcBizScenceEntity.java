package com.decathlon.sino.data.entity;

import com.decathlon.sino.data.entity.basic.IdEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_biz_scene")
public class RcBizScenceEntity extends IdEntity {
    private static final long serialVersionUID = 1L;

    private String classification;

    private String sceneName;

    private String description;

    private Date beginTime;

    private Date endTime;
    /**
     * 是否拦截
     * 不拦截，仅评估
     */
    private Boolean intercept;
    /**
     * true: 启用
     * false: 不启用
     */
    private Boolean status;

    private Date createTime;

    private Date updateTime;

    private String createBy;

    private String updateBy;

}
