package com.decathlon.sino.data.dao;

import java.util.List;

import com.decathlon.sino.data.dao.basic.QueryDslBaseDao;
import com.decathlon.sino.data.entity.RcBlackListReasonHitEntity;

public interface RcBlackListReasonHitEntityRepository extends QueryDslBaseDao<RcBlackListReasonHitEntity> {

	List<RcBlackListReasonHitEntity> findByBlackListId(Long id);
	
	RcBlackListReasonHitEntity findByBlackListIdAndRuleId(Long blackListId, Long ruleId);
	
		

}
