# 审计规则管理系统 - 所有接口的 curl 命令
# 基础URL: http://localhost:8282 || ip:************

#=============================================================================
# 1. 场景管理接口
#=============================================================================

# 1.1 创建审计场景
curl --location 'http://localhost:8282/portal/api/audit/scene' \
--header 'Content-Type: application/json' \
--data '{
    "classification": "风险控制",
    "scene_name": "用户注册",
    "scene_desc": "用户注册场景风险评估",
    "intercept_status": true,
    "enable_status": true,
    "create_by": "admin"
  }'

# 1.2 查询所有审计场景
curl --location 'http://localhost:8282/portal/api/audit/scene' \
--header 'Content-Type: application/json'

# 1.3 获取场景详情
curl --location 'http://localhost:8282/portal/api/audit/scene/detail?scene_id=4' \
--header 'Content-Type: application/json'

# 1.4 更新审计场景
curl --location --request PUT 'http://localhost:8282/portal/api/audit/scene' \
--header 'Content-Type: application/json' \
--data '{
    "scene_id": 4,
    "classification": "风险控制ddd",
    "scene_name": "用户注册",
    "scene_desc": "用户注册场景风险评估",
    "intercept_status": false,
    "enable_status": true,
    "create_by": "admin"
  }'

#=============================================================================
# 2. 规则参数接口
#=============================================================================

# 2.1 获取所有规则参数
curl --location 'http://localhost:8282/portal/api/audit/params' \
--header 'Content-Type: application/json'

#=============================================================================
# 3. 规则管理接口
#=============================================================================

# 3.1 查询规则列表 - 所有规则
curl --location 'http://localhost:8282/portal/api/audit/rule' \
--header 'Content-Type: application/json'

# 3.1 查询规则列表 - 按规则名称查询
curl --location 'http://localhost:8282/portal/api/audit/rule?rule_name=%E4%BC%9A%E5%91%98%E5%B8%B8%E5%8E%BB%E9%97%A8%E5%BA%97' \
--header 'Content-Type: application/json'

# 3.1 查询规则列表 - 按规则ID查询
curl --location 'http://localhost:8282/portal/api/audit/rule?id=5' \
--header 'Content-Type: application/json'

# 3.1 查询规则列表 - 按状态查询
curl --location 'http://localhost:8282/portal/api/audit/rule?status=true' \
--header 'Content-Type: application/json'

# 3.1 查询规则列表 - 组合查询
curl --location 'http://localhost:8282/portal/api/audit/rule?rule_name=%E8%BA%AB%E4%BB%BD%E8%AF%81%E9%AA%8C%E8%AF%81&status=true' \
--header 'Content-Type: application/json'

# 3.2 获取规则详情
curl --location 'http://localhost:8282/portal/api/audit/rule/detail?rule_id=5' \
--header 'Content-Type: application/json'

# 3.3 创建规则
curl --location 'http://localhost:8282/portal/api/audit/rule' \
--header 'Content-Type: application/json' \
--data '{
    "rule_name": "会员常去门店",
    "trigger_type": "个人信息变更",
    "expression": "#storeList.contains(#store)",
    "description": "会员常去门店属于相关门店",
    "status": true,
    "create_by": "admin",
    "system_params": [
      {
        "param_name": "storeList",
        "param_type": "list",
        "default_value": "[\"MP&官网\", \"Tmall\", \"JD\", \"抖音\", \"PDD\", \"线下门店\",\"JDDJ\"]",
        "description": "身份证号码",
        "enable_config": true
      },
      {
        "param_name": "store",
        "param_type": "string",
        "default_value": null,
        "description": "会员常去门店",
        "enable_config": false
      }

    ]
  }'

# 3.4 更新规则
curl --location --request PUT 'http://localhost:8282/portal/api/audit/rule' \
--header 'Content-Type: application/json' \
--data '{
    "rule_id":5,
    "rule_name": "会员常去门店",
    "trigger_type": "个人信息变更",
    "expression": "#storeList.contains(#store)",
    "description": "会员常去门店属于相关门店",
    "status": false,
    "create_by": "admin",
    "system_params": [
      {
        "param_name": "storeList",
        "param_type": "list",
        "default_value": "[\"MP&官网\", \"Tmall\", \"JD\", \"抖音\", \"PDD\", \"线下门店\",\"JDDJ\"]",
        "description": "身份证号码",
        "enable_config": true
      },
      {
        "param_name": "store",
        "param_type": "string",
        "default_value": null,
        "description": "会员常去门店",
        "enable_config": false
      }

    ]
  }'

#=============================================================================
# 4. 场景规则关联接口
#=============================================================================

# 4.1 绑定场景规则
curl --location --request POST 'http://localhost:8282/portal/api/audit/scene/rule?rule_id=5&scene_id=4' \
--header 'Content-Type: application/json'

# 4.2 解绑规则
curl --location --request DELETE 'http://localhost:8282/portal/api/audit/scene/rule?rule_id=5&scene_id=4' \
--header 'Content-Type: application/json'

# 4.3 更新场景规则（绑定逻辑）
curl -X PUT "http://localhost:8080/scence/rule" \
  -H "Content-Type: application/json" \
  -d '{
    "ruleId": 1,
    "sceneId": 1,
    "point": 50
  }'

#=============================================================================
# 5. 黑名单和命中记录接口
#=============================================================================

# 5.1 获取黑名单列表 - 基本查询
curl --location 'http://localhost:8282/portal/api/audit/blacklist?scene_id=4' \
--header 'Content-Type: application/json'

# 5.1 获取黑名单列表 - 带卡号查询
curl --location 'http://localhost:8282/portal/api/audit/blacklist?scene_id=4&card_number=123456789' \
--header 'Content-Type: application/json'

# 5.1 获取黑名单列表 - 带分页参数
curl --location 'http://localhost:8282/portal/api/audit/blacklist?scene_id=1&page=0&size=10&sort=createTime,desc" \
  -H "Accept: application/json"

# 5.2 获取规则命中记录（按黑名单ID）
curl --location 'http://localhost:8282/portal/api/audit/hitRecords?black_id=1' \
--header 'Content-Type: application/json'

# 5.3 获取规则命中记录（按规则ID）- 基本查询
curl --location 'http://localhost:8282/portal/api/audit/rule/hitRecords?rule_id=1' \
--header 'Content-Type: application/json'

# 5.3 获取规则命中记录（按规则ID）- 带卡号查询
curl --location 'http://localhost:8282/portal/api/audit/rule/hitRecords?rule_id=1&card_number=123456789" \
  -H "Accept: application/json"

# 5.3 获取规则命中记录（按规则ID）- 带分页参数
curl --location 'http://localhost:8282/portal/api/audit/rule/hitRecords?rule_id=1&page=0&size=10&sort=createTime,desc" \
  -H "Accept: application/json"

