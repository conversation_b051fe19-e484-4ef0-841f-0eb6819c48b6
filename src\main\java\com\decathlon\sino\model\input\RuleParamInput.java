package com.decathlon.sino.model.input;

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 系统参数DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(SnakeCaseStrategy.class)
@ApiModel(description = "系统参数信息")
public class RuleParamInput {

    @ApiModelProperty(value = "参数ID")
    private Long id;

    @ApiModelProperty(value = "参数名称", required = true)
    @NotBlank(message = "参数名称不能为空")
    private String paramName;

    @ApiModelProperty(value = "参数类型", required = true)
    @NotBlank(message = "参数类型不能为空")
    private String paramType;

    @ApiModelProperty(value = "默认值")
    private String defaultValue;
    
    private String value;

    @ApiModelProperty(value = "参数描述")
    private String description;

    @ApiModelProperty(value = "是否启用配置")
    @NotNull(message = "是否启用配置不能为空")
    private Boolean enableConfig;

}
