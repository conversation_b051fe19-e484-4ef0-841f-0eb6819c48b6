# 规则缓存组件优化说明

## 📋 优化概述

对 `RuleCacheComponent` 进行了全面重构和优化，以适应最新的数据表结构变化，特别是场景规则关联表 `rc_scene_rule_relation` 的引入。

## 🔄 主要变化

### 1. 数据结构适配
- **原有设计**: 基于简单的规则ID映射
- **新设计**: 支持场景规则关联，包含权重分数

### 2. 缓存架构升级
```java
// 全局规则缓存 - 所有启用的规则
Map<Long, RuleDefinition> globalRulesCache

// 场景规则缓存 - 按场景分组的规则
Map<Long, List<RuleDefinition>> sceneRulesCache
```

### 3. 线程安全优化
- 使用 `ConcurrentHashMap` 确保线程安全
- 原子性缓存更新操作
- 同步的缓存刷新机制

## 🚀 新增功能

### 1. 场景规则缓存
```java
// 获取指定场景的所有规则
List<RuleDefinition> rules = ruleCacheComponent.getRulesBySceneId(sceneId);

// 检查场景是否有关联规则
boolean hasRules = ruleCacheComponent.hasRulesForScene(sceneId);

// 获取场景规则数量
int count = ruleCacheComponent.getSceneRuleCount(sceneId);
```

### 2. 智能缓存刷新
```java
// 刷新单个规则缓存
ruleCacheComponent.refreshRule(ruleId);

// 刷新场景规则缓存
ruleCacheComponent.refreshSceneRules(sceneId);

// 重新加载所有缓存
ruleCacheComponent.reloadAll();
```

### 3. 缓存监控
```java
// 获取缓存统计信息
Map<String, Object> stats = ruleCacheComponent.getCacheStats();
/*
{
  "totalRules": 25,
  "totalScenes": 5,
  "sceneRuleCounts": {
    "scene_1": 8,
    "scene_2": 12,
    "scene_3": 5
  }
}
*/
```

## 🎯 使用示例

### 基本用法
```java
@Service
public class RiskEvaluationService {
    
    @Autowired
    private RuleCacheComponent ruleCacheComponent;
    
    public void evaluateRisk(Long sceneId, Map<String, Object> context) {
        // 获取场景相关的所有规则
        List<RuleDefinition> rules = ruleCacheComponent.getRulesBySceneId(sceneId);
        
        for (RuleDefinition rule : rules) {
            // 执行规则评估
            boolean matched = evaluateRule(rule, context);
            if (matched) {
                // 处理规则命中
                handleRuleHit(rule, context);
            }
        }
    }
    
    private boolean evaluateRule(RuleDefinition rule, Map<String, Object> context) {
        // 规则执行逻辑
        return true;
    }
}
```

### 缓存管理
```java
@Service
public class RuleCacheManagementService {
    
    @Autowired
    private RuleCacheComponent ruleCacheComponent;
    
    // 规则更新后刷新缓存
    public void onRuleUpdated(Long ruleId) {
        ruleCacheComponent.refreshRule(ruleId);
    }
    
    // 场景规则关联变更后刷新缓存
    public void onSceneRuleChanged(Long sceneId) {
        ruleCacheComponent.refreshSceneRules(sceneId);
    }
    
    // 获取缓存健康状态
    public Map<String, Object> getCacheHealth() {
        return ruleCacheComponent.getCacheStats();
    }
}
```

## 🔧 配置优化

### Spring Cache 配置
```yaml
spring:
  cache:
    type: redis
    redis:
      time-to-live: 3600000  # 1小时
      cache-null-values: false
    cache-names:
      - rules
      - sceneRules
```

### 缓存策略
- **全局规则缓存**: 长期缓存，手动失效
- **场景规则缓存**: 中期缓存，支持自动刷新
- **参数解析缓存**: 短期缓存，提高解析性能

## 📊 性能优化

### 1. 启动时预加载
- 系统启动时自动加载所有启用规则
- 构建场景规则映射关系
- 预解析规则参数默认值

### 2. 懒加载策略
- 场景规则按需加载
- 支持缓存预热
- 智能缓存失效

### 3. 内存优化
- 使用不可变对象减少内存占用
- 共享规则参数定义
- 及时清理无效缓存

## 🛡️ 错误处理

### 1. 缓存加载失败
```java
try {
    ruleCacheComponent.reloadAll();
} catch (RuntimeException e) {
    log.error("规则缓存加载失败，使用降级策略", e);
    // 降级到数据库直接查询
}
```

### 2. 参数解析异常
- 解析失败时返回原始字符串值
- 记录警告日志便于排查
- 不影响规则加载流程

### 3. 并发安全
- 使用线程安全的集合类
- 同步的缓存更新操作
- 原子性的缓存替换

## 🔄 迁移指南

### 从旧版本迁移
1. **API 变更**:
   ```java
   // 旧版本
   Map<String, RuleDefinition> rules = ruleCacheComponent.getRules();
   
   // 新版本
   Map<Long, RuleDefinition> rules = ruleCacheComponent.getAllRules();
   ```

2. **方法废弃**:
   ```java
   // 已废弃
   @Deprecated
   List<RuleDefinition> getAllByBiz(Long bizType);
   
   // 推荐使用
   List<RuleDefinition> getRulesBySceneId(Long sceneId);
   ```

3. **缓存键变更**:
   - 规则ID从 String 改为 Long
   - 新增场景维度的缓存

## 📈 监控建议

### 1. 关键指标
- 缓存命中率
- 缓存加载时间
- 规则执行性能
- 内存使用情况

### 2. 告警配置
- 缓存加载失败告警
- 缓存命中率过低告警
- 内存使用过高告警

### 3. 日志监控
```java
// 关键操作日志
log.info("规则缓存重新加载完成 - 全局规则: {}, 场景缓存: {}", 
         globalRulesCache.size(), sceneRulesCache.size());
```

## 🎯 最佳实践

1. **定期刷新**: 建议每小时自动刷新一次缓存
2. **监控告警**: 设置缓存相关的监控和告警
3. **降级策略**: 缓存失败时的数据库查询降级
4. **容量规划**: 根据规则数量合理配置缓存容量
5. **版本管理**: 规则变更时及时刷新相关缓存

---

**优化完成！** 🎉

新的规则缓存组件提供了更好的性能、更强的功能和更高的可靠性。
