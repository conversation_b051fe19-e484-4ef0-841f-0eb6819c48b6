package com.decathlon.sino.model.output;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 风险查询结果 - 直接查询模式的返回结果
 * 
 * 用于封装通过场景ID和对象ID直接查询黑名单的结果
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RiskQueryResult {
    
    /**
     * 场景ID
     */
    private Long sceneId;
    
    /**
     * 对象ID
     */
    private String objectId;
    
    /**
     * 是否在黑名单中
     */
    private Boolean inBlacklist;
    
    /**
     * 风险等级 (LOW/MEDIUM/HIGH)
     */
    private String riskLevel;
    
    /**
     * 总分数
     */
    private BigDecimal totalScore;
    
    /**
     * 命中规则数量
     */
    private Integer hitRuleCount;
    
    /**
     * 最后命中时间
     */
    private Date lastHitTime;
    
    /**
     * 查询时间
     */
    private Date queryTime;
    
    /**
     * 命中记录列表
     */
    private List<HitRecord> hitRecords;

    /**
     * 是否拦截
     */
    private Boolean blocked;
    
    /**
     * 命中记录详情
     */
    @Data
    public static class HitRecord {
        
        /**
         * 规则ID
         */
        private Long ruleId;
        
        /**
         * 命中时间
         */
        private Date hitTime;
        
        /**
         * 规则分数
         */
        private BigDecimal point;
        
        /**
         * 命中上下文
         */
        private String context;
    }
}
