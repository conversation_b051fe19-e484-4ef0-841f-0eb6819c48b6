package com.decathlon.sino.data.dao;

import com.decathlon.sino.data.dao.basic.QueryDslBaseDao;
import com.decathlon.sino.data.entity.RcScenceRuleEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface RcScenceRuleEntityRepository extends QueryDslBaseDao<RcScenceRuleEntity> {

    /**
     * 根据场景ID查询规则关联关系
     * @param sceneId 场景ID
     * @return 规则关联关系列表
     */
    List<RcScenceRuleEntity> findBySceneId(Long sceneId);

    /**
     * 根据规则ID查询规则关联关系
     * @param ruleId 规则ID
     * @return 规则关联关系列表
     */
    List<RcScenceRuleEntity> findByRuleId(Long ruleId);

    /**
     * 根据场景ID和规则ID查询规则关联关系
     * @param sceneId 场景ID
     * @param ruleId 规则ID
     * @return 规则关联关系
     */
    RcScenceRuleEntity findBySceneIdAndRuleId(Long sceneId, Long ruleId);

    /**
     * 根据规则ID删除规则关联关系
     * @param ruleId 规则ID
     */
    void deleteByRuleId(Long ruleId);

    /**
     * 根据场景ID删除规则关联关系
     * @param sceneId 场景ID
     */
    void deleteBySceneId(Long sceneId);
}
