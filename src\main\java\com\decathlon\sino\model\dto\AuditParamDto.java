package com.decathlon.sino.model.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonNaming(SnakeCaseStrategy.class)
public class AuditParamDto {
	
    @ApiModelProperty(value = "参数ID")
    private Long paramId;

    @ApiModelProperty(value = "参数名称", required = true)
    @NotBlank(message = "参数名称不能为空")
    private String paramName;

    @ApiModelProperty(value = "参数类型", required = true)
    @NotBlank(message = "参数类型不能为空")
    private String paramType;

    @ApiModelProperty(value = "默认值")
    private String defaultValue;
    
    private String value;

    @ApiModelProperty(value = "参数描述")
    private String description;

    @ApiModelProperty(value = "是否启用配置")
    @NotNull(message = "是否启用配置不能为空")
    private Boolean enableConfig;

    @ApiModelProperty(value = "是否已被规则使用")
    private Boolean used;

    @ApiModelProperty(value = "规则参数关联ID（仅已使用参数有值）")
    private Long ruleParamId;

}
