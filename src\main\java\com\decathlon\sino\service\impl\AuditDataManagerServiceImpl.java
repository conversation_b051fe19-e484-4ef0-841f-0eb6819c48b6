package com.decathlon.sino.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import com.alibaba.excel.EasyExcelFactory;
import com.decathlon.sino.common.config.PageResultDTO;
import com.decathlon.sino.component.excel.ImportListExcelListener;
import com.decathlon.sino.data.dao.RcListImportEntityRepository;
import com.decathlon.sino.data.entity.RcListImportEntity;
import com.decathlon.sino.kafka.biz.helper.dto.AuditMessageDto;
import com.decathlon.sino.model.bo.ImportRow;
import com.decathlon.sino.model.criteria.ListDataSearchCriteria;
import com.decathlon.sino.model.ouput.RiskInfoOutput;
import com.decathlon.sino.service.AuditDataManagerService;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AuditDataManagerServiceImpl implements AuditDataManagerService {
	
	@Value("${member.platform.host:http://member-platform-cn.dktapp.cloud/}")
	private String memberPlatformHost;
	
	@Autowired
	private RcListImportEntityRepository rcListImportEntityRepository;
	
	@Autowired
	private RestTemplate restTemplate;

	@Override
	public List<ImportRow> refineImportData(MultipartFile file) {
		
		List<ImportRow> result = new ArrayList<>();
		
        ImportListExcelListener listener = new ImportListExcelListener();
        try {
            EasyExcelFactory.read(file.getInputStream(), ImportRow.class, listener).sheet().doRead();
        } catch (IOException e) {
            log.error("Excel文件读取失败", e);
        }
        
        List<ImportRow> importRows = listener.getDataList();
        if (importRows == null || importRows.isEmpty()) {
			log.warn("导入数据为空或未找到有效数据");
			return result;
		}
        
        refineImportData(result, importRows);
        // 返回的数据按行索引排序
		result.sort(Comparator.comparing(ImportRow::getRowIndex));
		return result;
	}

	/**
	 * 分批次处理导入数据，避免一次性处理过多数据导致性能问题
	 * @param result 存储处理后的结果
	 * @param importRows 导入的原始数据
	 */
	private void refineImportData(List<ImportRow> result, List<ImportRow> importRows) {
		if(importRows.size() > 2000) {
        	Integer size = 0;
        	while(size<importRows.size()) {
        		List<ImportRow> subList = importRows.subList(size, Math.min(size + 2000, importRows.size()));
        		List<ImportRow> refinedSubList = refineImportDataByRemote(subList);
        		backfill(subList, refinedSubList);
        		result.addAll(refinedSubList);
        		size = size+2000;
			}
        }else {
        	List<ImportRow> refinedSubList = refineImportDataByRemote(importRows);
    		backfill(importRows, refinedSubList);
        	result.addAll(refinedSubList);
        }
	}

	/**
	 * 回填原始数据中的reason到精炼后的数据中
	 * @param subList 原始数据列表
	 * @param refinedSubList 精炼后的数据列表
	 */
	private void backfill(List<ImportRow> subList, List<ImportRow> refinedSubList) {
		// 根据 rowIndex 回填reason 内容
		for (ImportRow refinedRow : refinedSubList) {
			for (ImportRow originalRow : subList) {
				if (refinedRow.getRowIndex().equals(originalRow.getRowIndex())) {
					refinedRow.setReason(originalRow.getReason());
					break;
				}
			}
		}
	}

	/**
	 * 通过远程调用精炼导入数据
	 * @param importRows 导入的原始数据
	 * @return 精炼后的数据列表
	 */
	@Retryable(maxAttempts = 3, include = ResourceAccessException.class)
	private List<ImportRow> refineImportDataByRemote(List<ImportRow> importRows) {
		HttpHeaders headers = new HttpHeaders();
		String url = UriComponentsBuilder.fromHttpUrl(memberPlatformHost.concat("identity/api/v1/account/batch/refine")).build().toUriString();
		String response = restTemplate.exchange(url, 
				HttpMethod.POST, 
				new HttpEntity<>(importRows, headers), String.class).getBody();
		List<ImportRow> refinedRows = new ArrayList<>();
		
		JSONUtil.parseObj(response).getJSONArray("data").forEach(item -> {
			ImportRow importRow = JSONUtil.toBean(JSONUtil.parse(item).toString(), ImportRow.class);
			refinedRows.add(importRow);
		});
		
		return refinedRows;
	}
	
	/** 
	 * 查询用户审核信息
	 */
	@Retryable(maxAttempts = 3, include = ResourceAccessException.class)
	public AuditMessageDto queryInfo(String type,String value) {
		HttpHeaders headers = new HttpHeaders();
		String url = UriComponentsBuilder.fromHttpUrl(memberPlatformHost.concat("identity/api/v1/account/audit")).build().toUriString();
		return restTemplate.exchange(url, 
				HttpMethod.GET, 
				new HttpEntity<>(headers), AuditMessageDto.class).getBody();
	}
	
	/** 
	 * 查询用户店号审核信息
	 */
	@Retryable(maxAttempts = 3, include = ResourceAccessException.class)
	public AuditMessageDto queryStore(Long personId) {
		HttpHeaders headers = new HttpHeaders();
		String url = UriComponentsBuilder.fromHttpUrl(memberPlatformHost.concat("store/api/v1/store/audit")).build().toUriString();
		return restTemplate.exchange(url, 
				HttpMethod.GET, 
				new HttpEntity<>(headers), AuditMessageDto.class).getBody();
	}

	@Override
	public PageResultDTO<ImportRow> queryRisks(ListDataSearchCriteria searchCriteria) {
		List<ImportRow> result = new ArrayList<>();
		Page<RcListImportEntity> page = rcListImportEntityRepository.findAll(searchCriteria.toPredicate(), searchCriteria.getPageable());
		for (RcListImportEntity rcListImportEntity : page) {
			ImportRow importRow = new ImportRow();
			importRow.setCardNumber(rcListImportEntity.getCardNumber());
			importRow.setMobile(rcListImportEntity.getMobile());
			importRow.setPersonId(rcListImportEntity.getPersonId() != null ? rcListImportEntity.getPersonId().toString() : null);
			importRow.setReason(rcListImportEntity.getReason());
			importRow.setCreatedTime(rcListImportEntity.getCreatedTime());
			importRow.setOperator(rcListImportEntity.getOperator());
			result.add(importRow);
		}
		return PageResultDTO.of(page, result);

	}


	@Override
	public List<RiskInfoOutput> riskStatics(String bizType) {
		List<RiskInfoOutput> riskInfoOutput = new ArrayList<>();
		List<RcListImportEntity> rcListImportEntities = rcListImportEntityRepository.findByType(bizType);
		if (rcListImportEntities == null || rcListImportEntities.isEmpty()) {
			log.warn("No risk data found for bizType: {}", bizType);
			return riskInfoOutput;
		}
		
		// 统计逻辑,计算黑白名单的数量
		Integer statusTrueCount = 0;
		Integer statusFalseCount = 0;
		for (RcListImportEntity entity : rcListImportEntities) {
			if (entity.getStatus() != null && entity.getStatus()) {
				statusTrueCount++;
			} else {
				statusFalseCount++;
			}
		}
		// 白名单输出
		if(statusFalseCount>0) {
			RiskInfoOutput riskInfo = new RiskInfoOutput();
			riskInfo.setBizType(bizType);
			riskInfo.setNum(statusFalseCount);
			riskInfo.setStatus(false);
			riskInfoOutput.add(riskInfo);
		}
		
		// 黑名单输出
		if(statusTrueCount>0) {
			RiskInfoOutput riskInfo = new RiskInfoOutput();
			riskInfo.setBizType(bizType);
			riskInfo.setNum(statusTrueCount);
			riskInfo.setStatus(true);
			riskInfoOutput.add(riskInfo);
		}
		
		return riskInfoOutput;
	}

	@Override
	public String importRiskList(List<ImportRow> importListDto, String bizType, String operator, Boolean status) {
			List<RcListImportEntity> rcListImportEntities = new ArrayList<>();
			Set<String> personIds = new HashSet<>();
			for (ImportRow row : importListDto) {
				RcListImportEntity rcListImportEntity = new RcListImportEntity();
				rcListImportEntity.setCardNumber(row.getCardNumber());
				rcListImportEntity.setCreatedTime(new Date());
				rcListImportEntity.setMobile(row.getMobile());
				rcListImportEntity.setOperator(operator);
				rcListImportEntity.setPersonId(Long.valueOf(row.getPersonId()));
				rcListImportEntity.setReason(row.getReason());
				rcListImportEntity.setStatus(status);
				rcListImportEntity.setType(bizType);
//				rcListImportEntity.setEffectiveStartTime(row.getEffectiveStartTime());
//				rcListImportEntity.setEffectiveEndTime(row.getEffectiveEndTime());
				if(personIds.contains(row.getPersonId())) {
					log.warn("Duplicate personId found: {}", row.getPersonId());
				}else {
					RcListImportEntity dbEntity =rcListImportEntityRepository.findByTypeAndPersonId(bizType, Long.valueOf(row.getPersonId()));
					if(dbEntity == null) {
						rcListImportEntities.add(rcListImportEntity);
						personIds.add(row.getPersonId());
					} 
				}
			}
			rcListImportEntityRepository.saveAllAndFlush(rcListImportEntities);
			//将这部分的数据分拆到global risk list中
			return "finished import risk list, total: " + rcListImportEntities.size() + " records.";
	}

	@Override
	public ImportRow refineData(ImportRow importRow) {
		List<ImportRow> data = new ArrayList<>();
		data.add(importRow);
		List<ImportRow> refinedSubList = refineImportDataByRemote(data);
		return !refinedSubList.isEmpty() ? refinedSubList.get(0) : null;
	}

	@Override
	public String deleteRiskList(Long personId, String bizType, String operator, Boolean status) {
		rcListImportEntityRepository.deleteByTypeAndPersonId(bizType, personId);
		return "successfully deleted risk list for type: " + bizType + ", operator: " + operator + ", status: " + status;
	}

	@Override
	public String importRiskList(List<ImportRow> importListDto, String bizType, String objectType, String operator,Boolean status) {
		return null;
	}

}
