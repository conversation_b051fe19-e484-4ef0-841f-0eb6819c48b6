package com.decathlon.sino.model.ouput;

import java.util.List;

import com.decathlon.sino.model.dto.AuditParamDto;
import com.decathlon.sino.model.dto.AuditRuleBaseInfoDto;
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
@JsonNaming(SnakeCaseStrategy.class)
public class AuditRuleOutput extends AuditRuleBaseInfoDto{
	
	// 包含所有的参数、
	// 提交参数的时候，修改过的，没有修改的都要提交，方便后续的拉去操作
    private List<AuditParamDto> paramList;
    private String expression;

}
