package com.decathlon.sino.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.decathlon.sino.common.exception.ServiceException;
import com.decathlon.sino.component.RiskEngineComponent;
import com.decathlon.sino.data.dao.RcBizScenceEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListReasonHitEntityRepository;
import com.decathlon.sino.data.dao.RcBlacklistGlobalEntityRepository;
import com.decathlon.sino.model.output.RiskEvaluationResult;
import com.decathlon.sino.model.output.RiskQueryResult;
import com.decathlon.sino.service.AuditService;
import com.decathlon.sino.service.impl.helper.PurchaseRiskHelper;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 * 购买审计服务实现
 *
 * 专门处理购买相关的风险审计，包括：
 * - 购买行为风险评估
 * - 异常购买模式检测
 * - 购买欺诈行为识别
 * - 支付风险评估
 * - 商品购买异常检测
 *
 * 继承自 AduitProcessService，复用基础的风险查询和评估功能
 */
@Service
@Slf4j
public class PurchaseAduitServiceImpl extends AduitProcessService implements AuditService {

	// 购买事件数据字段常量
	private static final String FIELD_ORDER_AMOUNT = "orderAmount";
	private static final String FIELD_TOTAL_AMOUNT = "totalAmount";
	private static final String FIELD_PAYMENT_METHOD = "paymentMethod";
	private static final String FIELD_PRODUCT_ID = "productId";
	private static final String FIELD_PRODUCT_CATEGORY = "productCategory";
	private static final String FIELD_QUANTITY = "quantity";
	private static final String FIELD_DISCOUNT_AMOUNT = "discountAmount";
	private static final String FIELD_SHIPPING_ADDRESS = "shippingAddress";
	private static final String FIELD_BILLING_ADDRESS = "billingAddress";
	private static final String FIELD_ORDER_TIME = "orderTime";
	private static final String FIELD_USER_AGENT = "userAgent";
	private static final String FIELD_IP_ADDRESS = "ipAddress";

	// 购买金额等级常量
	private static final String AMOUNT_LEVEL_INVALID = "INVALID";
	private static final String AMOUNT_LEVEL_LOW = "LOW";
	private static final String AMOUNT_LEVEL_MEDIUM = "MEDIUM";
	private static final String AMOUNT_LEVEL_HIGH = "HIGH";
	private static final String AMOUNT_LEVEL_VERY_HIGH = "VERY_HIGH";

	// 风险阈值常量
	private static final double HIGH_AMOUNT_THRESHOLD = 10000.0;
	private static final double VERY_HIGH_AMOUNT_THRESHOLD = 50000.0;
	private static final int HIGH_QUANTITY_THRESHOLD = 10;

	/**
	 * 构造函数 - 注入所需的依赖
	 */
	public PurchaseAduitServiceImpl(
			RiskEngineComponent riskEngineComponent,
			RcBlackListEntityRepository rcBlackListEntityRepository,
			RcBlackListReasonHitEntityRepository rcBlackListReasonHitEntityRepository,
			RcBizScenceEntityRepository rcBizScenceEntityRepository,
			RcBlacklistGlobalEntityRepository rcGlobalBlackListEntityRepository) {
		super(riskEngineComponent, rcBlackListEntityRepository, rcBlackListReasonHitEntityRepository,
			  rcBizScenceEntityRepository, rcGlobalBlackListEntityRepository);
	}

	/**
	 * 购买风险检查
	 *
	 * @param sceneId 场景ID
	 * @param objectId 对象ID（如用户ID、订单ID等）
	 * @param eventData 事件数据（包含购买相关信息）
	 * @param operator 操作人
	 * @param enableAudit 是否启用审计模式
	 * @return 风险查询结果
	 */
	@Override
	public RiskQueryResult checkRisk(Long sceneId, String objectId, JSONObject eventData, String operator, Boolean enableAudit) {
		log.info("开始购买风险检查: sceneId={}, objectId={}, enableAudit={}", sceneId, objectId, enableAudit);

		try {
			// 验证购买事件数据
			validatePurchaseEventData(eventData);

			if (Boolean.TRUE.equals(enableAudit)) {
				// 审计模式：先评估再返回结果
				log.debug("使用审计模式进行购买风险评估");
				RiskEvaluationResult evaluationResult = this.auditRisk(sceneId, objectId, eventData, operator, enableAudit);

				// 将评估结果转换为查询结果格式
				return convertEvaluationToQueryResult(evaluationResult);
			} else {
				// 直接查询模式：快速查询现有风险状态
				log.debug("使用直接查询模式检查购买风险");
				return super.checkRisk(sceneId, objectId);
			}
		} catch (Exception e) {
			log.error("购买风险检查失败: sceneId={}, objectId={}", sceneId, objectId, e);
			throw new ServiceException("购买风险检查失败", e);
		}
	}

	/**
	 * 购买风险审计评估
	 *
	 * @param sceneId 场景ID
	 * @param objectId 对象ID
	 * @param eventData 事件数据（包含购买相关信息）
	 * @param operator 操作人
	 * @param enableRecord 是否记录评估结果
	 * @return 风险评估结果
	 */
	@Override
	public RiskEvaluationResult auditRisk(Long sceneId, String objectId, JSONObject eventData, String operator, Boolean enableRecord) {
		log.info("开始购买风险审计评估: sceneId={}, objectId={}, enableRecord={}", sceneId, objectId, enableRecord);

		try {
			// 1. 验证购买事件数据
			validatePurchaseEventData(eventData);

			// 2. 提取购买相关的上下文信息
			Map<String, Object> purchaseContext = extractPurchaseContext(eventData);
			log.debug("购买上下文提取完成: {}", purchaseContext);

			// 3. 调用父类的审计评估方法
			RiskEvaluationResult result = super.auditRisk(sceneId, objectId, purchaseContext, operator, enableRecord);

			// 4. 增强购买特定的风险信息
			enhancePurchaseRiskInfo(result, eventData);

			log.info("购买风险审计评估完成: sceneId={}, objectId={}, hitCount={}",
					sceneId, objectId, result.getHitRuleCount());

			return result;
		} catch (Exception e) {
			log.error("购买风险审计评估失败: sceneId={}, objectId={}", sceneId, objectId, e);
			throw new ServiceException("购买风险审计评估失败", e);
		}
	}
	/**
	 * 验证购买事件数据
	 *
	 * @param eventData 事件数据
	 */
	private void validatePurchaseEventData(JSONObject eventData) {
		if (eventData == null) {
			log.warn("购买事件数据为空");
			return;
		}

		// 验证必要的购买字段
		if (!eventData.containsKey(FIELD_ORDER_AMOUNT) && !eventData.containsKey(FIELD_TOTAL_AMOUNT)) {
			log.warn("购买事件数据中缺少订单金额字段");
		}

		// 验证支付方式
		if (!eventData.containsKey(FIELD_PAYMENT_METHOD)) {
			log.warn("购买事件数据中缺少支付方式字段");
		}

		// 验证商品信息
		if (!eventData.containsKey(FIELD_PRODUCT_ID)) {
			log.warn("购买事件数据中缺少商品ID字段");
		}

		log.debug("购买事件数据验证完成");
	}

	/**
	 * 提取购买相关的上下文信息
	 *
	 * @param eventData 事件数据
	 * @return 购买上下文信息
	 */
	private Map<String, Object> extractPurchaseContext(JSONObject eventData) {
		if (eventData == null) {
			log.warn("事件数据为空，使用空的购买上下文");
			return Map.of();
		}

		try {
			// 使用 PurchaseRiskHelper 提取购买上下文
			Map<String, Object> context = PurchaseRiskHelper.getContext(eventData);
			log.debug("购买上下文提取成功: keys={}", context.keySet());

			// 添加额外的购买风险评估参数
			enrichPurchaseContext(context, eventData);

			return context;
		} catch (Exception e) {
			log.error("购买上下文提取失败", e);
			return Map.of();
		}
	}

	/**
	 * 丰富购买上下文信息
	 *
	 * @param context 上下文信息
	 * @param eventData 事件数据
	 */
	private void enrichPurchaseContext(Map<String, Object> context, JSONObject eventData) {
		// 添加订单金额相关信息
		addAmountInfo(context, eventData);

		// 添加商品相关信息
		addProductInfo(context, eventData);

		// 添加支付相关信息
		addPaymentInfo(context, eventData);

		// 添加地址相关信息
		addAddressInfo(context, eventData);

		// 添加设备和环境信息
		addDeviceInfo(context, eventData);

		log.debug("购买上下文信息丰富完成");
	}

	/**
	 * 添加金额相关信息
	 */
	private void addAmountInfo(Map<String, Object> context, JSONObject eventData) {
		// 订单金额
		if (eventData.containsKey(FIELD_ORDER_AMOUNT)) {
			Object orderAmount = eventData.get(FIELD_ORDER_AMOUNT);
			context.put(FIELD_ORDER_AMOUNT, orderAmount);

			// 计算金额等级
			if (orderAmount instanceof Number) {
				double amount = ((Number) orderAmount).doubleValue();
				context.put("orderAmountLevel", calculateAmountLevel(amount));
			}
		} else if (eventData.containsKey(FIELD_TOTAL_AMOUNT)) {
			Object totalAmount = eventData.get(FIELD_TOTAL_AMOUNT);
			context.put(FIELD_ORDER_AMOUNT, totalAmount);

			if (totalAmount instanceof Number) {
				double amount = ((Number) totalAmount).doubleValue();
				context.put("orderAmountLevel", calculateAmountLevel(amount));
			}
		}

		// 折扣金额
		if (eventData.containsKey(FIELD_DISCOUNT_AMOUNT)) {
			context.put(FIELD_DISCOUNT_AMOUNT, eventData.get(FIELD_DISCOUNT_AMOUNT));
		}
	}

	/**
	 * 添加商品相关信息
	 */
	private void addProductInfo(Map<String, Object> context, JSONObject eventData) {
		// 商品ID
		if (eventData.containsKey(FIELD_PRODUCT_ID)) {
			context.put(FIELD_PRODUCT_ID, eventData.getStr(FIELD_PRODUCT_ID));
		}

		// 商品类别
		if (eventData.containsKey(FIELD_PRODUCT_CATEGORY)) {
			context.put(FIELD_PRODUCT_CATEGORY, eventData.getStr(FIELD_PRODUCT_CATEGORY));
		}

		// 购买数量
		if (eventData.containsKey(FIELD_QUANTITY)) {
			Object quantity = eventData.get(FIELD_QUANTITY);
			context.put(FIELD_QUANTITY, quantity);

			// 计算数量等级
			if (quantity instanceof Number) {
				int qty = ((Number) quantity).intValue();
				context.put("quantityLevel", calculateQuantityLevel(qty));
			}
		}
	}

	/**
	 * 添加支付相关信息
	 */
	private void addPaymentInfo(Map<String, Object> context, JSONObject eventData) {
		// 支付方式
		if (eventData.containsKey(FIELD_PAYMENT_METHOD)) {
			context.put(FIELD_PAYMENT_METHOD, eventData.getStr(FIELD_PAYMENT_METHOD));
		}
	}

	/**
	 * 添加地址相关信息
	 */
	private void addAddressInfo(Map<String, Object> context, JSONObject eventData) {
		// 收货地址
		if (eventData.containsKey(FIELD_SHIPPING_ADDRESS)) {
			context.put(FIELD_SHIPPING_ADDRESS, eventData.get(FIELD_SHIPPING_ADDRESS));
		}

		// 账单地址
		if (eventData.containsKey(FIELD_BILLING_ADDRESS)) {
			context.put(FIELD_BILLING_ADDRESS, eventData.get(FIELD_BILLING_ADDRESS));
		}
	}

	/**
	 * 添加设备和环境信息
	 */
	private void addDeviceInfo(Map<String, Object> context, JSONObject eventData) {
		// IP地址
		if (eventData.containsKey(FIELD_IP_ADDRESS)) {
			context.put(FIELD_IP_ADDRESS, eventData.getStr(FIELD_IP_ADDRESS));
		}

		// 用户代理
		if (eventData.containsKey(FIELD_USER_AGENT)) {
			context.put(FIELD_USER_AGENT, eventData.getStr(FIELD_USER_AGENT));
		}

		// 订单时间
		if (eventData.containsKey(FIELD_ORDER_TIME)) {
			context.put(FIELD_ORDER_TIME, eventData.get(FIELD_ORDER_TIME));
		}
	}
	/**
	 * 计算订单金额等级
	 *
	 * @param amount 订单金额
	 * @return 金额等级
	 */
	private String calculateAmountLevel(double amount) {
		if (amount <= 0) {
			return AMOUNT_LEVEL_INVALID;
		} else if (amount <= 100) {
			return AMOUNT_LEVEL_LOW;
		} else if (amount <= 1000) {
			return AMOUNT_LEVEL_MEDIUM;
		} else if (amount <= HIGH_AMOUNT_THRESHOLD) {
			return AMOUNT_LEVEL_HIGH;
		} else {
			return AMOUNT_LEVEL_VERY_HIGH;
		}
	}

	/**
	 * 计算购买数量等级
	 *
	 * @param quantity 购买数量
	 * @return 数量等级
	 */
	private String calculateQuantityLevel(int quantity) {
		if (quantity <= 0) {
			return AMOUNT_LEVEL_INVALID;
		} else if (quantity <= 1) {
			return AMOUNT_LEVEL_LOW;
		} else if (quantity <= 5) {
			return AMOUNT_LEVEL_MEDIUM;
		} else if (quantity <= HIGH_QUANTITY_THRESHOLD) {
			return AMOUNT_LEVEL_HIGH;
		} else {
			return AMOUNT_LEVEL_VERY_HIGH;
		}
	}

	/**
	 * 增强购买特定的风险信息
	 *
	 * @param result 风险评估结果
	 * @param eventData 原始事件数据
	 */
	private void enhancePurchaseRiskInfo(RiskEvaluationResult result, JSONObject eventData) {
		// 这里可以添加购买特定的风险信息增强逻辑
		// 例如：购买模式分析、异常行为检测等

		if (eventData != null) {
			// 记录订单金额
			if (eventData.containsKey(FIELD_ORDER_AMOUNT)) {
				log.debug("订单金额: {}", eventData.get(FIELD_ORDER_AMOUNT));
			}

			// 记录支付方式
			if (eventData.containsKey(FIELD_PAYMENT_METHOD)) {
				log.debug("支付方式: {}", eventData.getStr(FIELD_PAYMENT_METHOD));
			}

			// 记录商品信息
			if (eventData.containsKey(FIELD_PRODUCT_ID)) {
				log.debug("商品ID: {}", eventData.getStr(FIELD_PRODUCT_ID));
			}

			// 执行购买风险模式分析
			analyzePurchaseRiskPatterns(result, eventData);
		}
	}

	/**
	 * 分析购买风险模式
	 *
	 * @param result 风险评估结果
	 * @param eventData 事件数据
	 */
	private void analyzePurchaseRiskPatterns(RiskEvaluationResult result, JSONObject eventData) {
		// 分析高额订单风险
		analyzeHighAmountRisk(eventData);

		// 分析异常数量风险
		analyzeQuantityRisk(eventData);

		// 分析支付方式风险
		analyzePaymentMethodRisk(eventData);

		// 分析地址风险
		analyzeAddressRisk(eventData);
	}

	/**
	 * 分析高额订单风险
	 */
	private void analyzeHighAmountRisk(JSONObject eventData) {
		if (eventData.containsKey(FIELD_ORDER_AMOUNT)) {
			Object amount = eventData.get(FIELD_ORDER_AMOUNT);
			if (amount instanceof Number) {
				double orderAmount = ((Number) amount).doubleValue();
				if (orderAmount > VERY_HIGH_AMOUNT_THRESHOLD) {
					log.warn("检测到超高额订单: {}", orderAmount);
				} else if (orderAmount > HIGH_AMOUNT_THRESHOLD) {
					log.info("检测到高额订单: {}", orderAmount);
				}
			}
		}
	}

	/**
	 * 分析异常数量风险
	 */
	private void analyzeQuantityRisk(JSONObject eventData) {
		if (eventData.containsKey(FIELD_QUANTITY)) {
			Object quantity = eventData.get(FIELD_QUANTITY);
			if (quantity instanceof Number) {
				int qty = ((Number) quantity).intValue();
				if (qty > HIGH_QUANTITY_THRESHOLD) {
					log.warn("检测到异常购买数量: {}", qty);
				}
			}
		}
	}

	/**
	 * 分析支付方式风险
	 */
	private void analyzePaymentMethodRisk(JSONObject eventData) {
		if (eventData.containsKey(FIELD_PAYMENT_METHOD)) {
			String paymentMethod = eventData.getStr(FIELD_PAYMENT_METHOD);
			log.debug("支付方式分析: {}", paymentMethod);

			// 这里可以添加特定支付方式的风险分析逻辑
			// 例如：某些支付方式的风险等级较高
		}
	}

	/**
	 * 分析地址风险
	 */
	private void analyzeAddressRisk(JSONObject eventData) {
		// 分析收货地址和账单地址是否一致
		if (eventData.containsKey(FIELD_SHIPPING_ADDRESS) && eventData.containsKey(FIELD_BILLING_ADDRESS)) {
			Object shippingAddr = eventData.get(FIELD_SHIPPING_ADDRESS);
			Object billingAddr = eventData.get(FIELD_BILLING_ADDRESS);

			if (!shippingAddr.equals(billingAddr)) {
				log.debug("检测到收货地址与账单地址不一致");
			}
		}
	}

	/**
	 * 将评估结果转换为查询结果格式
	 *
	 * @param evaluationResult 评估结果
	 * @return 查询结果
	 */
	private RiskQueryResult convertEvaluationToQueryResult(RiskEvaluationResult evaluationResult) {
		RiskQueryResult queryResult = new RiskQueryResult();
		queryResult.setSceneId(evaluationResult.getSceneId());
		queryResult.setObjectId(evaluationResult.getObjectId());
		queryResult.setInBlacklist(evaluationResult.getInBlacklist());
		queryResult.setRiskLevel(evaluationResult.getCurrentRiskLevel());
		queryResult.setTotalScore(evaluationResult.getCurrentTotalScore());
		queryResult.setHitRuleCount(evaluationResult.getHitRuleCount());
		queryResult.setBlocked(evaluationResult.getBlocked());
		queryResult.setQueryTime(evaluationResult.getEvaluationTime());

		// 设置命中记录为空（审计模式下的新命中记录不包含在查询结果中）
		queryResult.setHitRecords(java.util.Collections.emptyList());
		queryResult.setLastHitTime(null);

		return queryResult;
	}
}
