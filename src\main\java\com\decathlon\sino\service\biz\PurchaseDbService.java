package com.decathlon.sino.service.biz;

import com.decathlon.sino.data.entity.biz.AccountPointEntity;
import com.decathlon.sino.data.entity.biz.PurchaseEntity;
import org.nrf_arts.ixretail.v6_0_0.poslog.TransactionDomainSpecific;

import java.math.BigDecimal;
import java.util.List;

public interface PurchaseDbService {
    Boolean save(PurchaseEntity purchaseEntity);

    Double getSalePrice(TransactionDomainSpecific orderSpecific);

    Double getReturnPrice(TransactionDomainSpecific orderSpecific);

    PurchaseEntity findByTransactionId(String transactionId);

    List<PurchaseEntity> getLastByCardNumber(String cardNo, Integer limit);
}
