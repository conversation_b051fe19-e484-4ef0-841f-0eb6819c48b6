# 黑名单场景化修正说明

## 🔧 问题描述

原来的黑名单创建逻辑只基于 `objectId` 查找和创建黑名单记录，这样会导致：

1. **数据混乱**: 同一个对象在不同场景下的风险记录会混在一起
2. **业务不准确**: 无法区分对象在不同场景下的风险状态
3. **查询困难**: 无法按场景维度查询和管理黑名单

## ✅ 修正方案

### 1. 黑名单创建逻辑调整

**修正前**:
```java
// 只基于 objectId 查找
RcBlackListEntity existingBlackList = rcBlackListEntityRepository
    .findByObjectId(hitResult.getObjectId());
```

**修正后**:
```java
// 基于 sceneId + objectId 组合查找
RcBlackListEntity existingBlackList = rcBlackListEntityRepository
    .findByBizTypeIdAndObjectId(sceneId, hitResult.getObjectId());
```

### 2. 方法签名调整

**修正前**:
```java
private Long findOrCreateBlackList(RuleHitResult hitResult)
private void recordRuleHit(RuleHitResult hitResult)
private RuleHitResult evaluateSingleRule(RuleDefinition rule, String objectId, ...)
```

**修正后**:
```java
private Long findOrCreateBlackList(RuleHitResult hitResult, Long sceneId)
private void recordRuleHit(RuleHitResult hitResult, Long sceneId)
private RuleHitResult evaluateSingleRule(RuleDefinition rule, String objectId, ..., Long sceneId)
```

## 🎯 核心改进

### 1. 场景化黑名单管理

```java
/**
 * 查找或创建黑名单记录
 * 黑名单基于场景ID和对象ID的组合创建
 */
private Long findOrCreateBlackList(RuleHitResult hitResult, Long sceneId) {
    // 查找是否已存在黑名单记录（基于场景ID和对象ID）
    RcBlackListEntity existingBlackList = rcBlackListEntityRepository
            .findByBizTypeIdAndObjectId(sceneId, hitResult.getObjectId());
    
    if (existingBlackList != null) {
        // 更新现有记录
        existingBlackList.setUpdateTime(new Date());
        if (hitResult.getPoint() != null) {
            // 累加分数（如果需要）
            BigDecimal currentScore = existingBlackList.getFinalScore() != null ?
                    existingBlackList.getFinalScore() : BigDecimal.ZERO;
            existingBlackList.setFinalScore(currentScore.add(hitResult.getPoint()));
        }
        rcBlackListEntityRepository.save(existingBlackList);
        return existingBlackList.getId();
    } else {
        // 创建新的黑名单记录
        RcBlackListEntity newBlackList = new RcBlackListEntity();
        newBlackList.setBizTypeId(sceneId);  // 设置场景ID
        newBlackList.setObjectId(hitResult.getObjectId());
        newBlackList.setFinalScore(hitResult.getPoint() != null ? hitResult.getPoint() : BigDecimal.ZERO);
        newBlackList.setCreateTime(new Date());
        newBlackList.setUpdateTime(new Date());
        
        RcBlackListEntity savedBlackList = rcBlackListEntityRepository.save(newBlackList);
        return savedBlackList.getId();
    }
}
```

### 2. 数据库表结构对应

```sql
-- rc_black_list 表结构
CREATE TABLE rc_black_list (
    id BIGINT PRIMARY KEY,
    biz_type_id BIGINT NOT NULL,  -- 场景ID
    object_id VARCHAR(255) NOT NULL,  -- 对象ID
    final_score DECIMAL(10,2),  -- 最终分数
    create_time TIMESTAMP,
    update_time TIMESTAMP,
    
    -- 组合唯一索引，确保同一场景下同一对象只有一条记录
    UNIQUE KEY uk_biz_object (biz_type_id, object_id)
);
```

### 3. 业务逻辑优势

#### 场景隔离
- **用户注册场景**: 用户A在注册场景被加入黑名单
- **订单支付场景**: 同一用户A在支付场景可能没有风险
- **数据独立**: 两个场景的风险评估和黑名单管理完全独立

#### 精确管理
```java
// 查询特定场景下的黑名单
List<RcBlackListEntity> registrationBlacklist = 
    rcBlackListEntityRepository.findByBizTypeId(REGISTRATION_SCENE_ID);

// 查询用户在特定场景下的风险状态
RcBlackListEntity userRisk = 
    rcBlackListEntityRepository.findByBizTypeIdAndObjectId(sceneId, userId);
```

## 📊 使用示例

### 1. 多场景风险评估

```java
@Service
public class MultiSceneRiskService {
    
    @Autowired
    private RiskEngineComponent riskEngineComponent;
    
    public void evaluateUserAcrossScenes(String userId, Map<String, Object> userInfo) {
        // 评估用户注册风险
        List<RuleHitResult> registrationHits = riskEngineComponent.evaluateRules(
            REGISTRATION_SCENE_ID, userId, userInfo, "system", true
        );
        
        // 评估用户支付风险
        List<RuleHitResult> paymentHits = riskEngineComponent.evaluateRules(
            PAYMENT_SCENE_ID, userId, userInfo, "system", true
        );
        
        // 评估用户登录风险
        List<RuleHitResult> loginHits = riskEngineComponent.evaluateRules(
            LOGIN_SCENE_ID, userId, userInfo, "system", true
        );
        
        // 每个场景的风险评估结果独立处理
        handleRegistrationRisk(userId, registrationHits);
        handlePaymentRisk(userId, paymentHits);
        handleLoginRisk(userId, loginHits);
    }
}
```

### 2. 场景化黑名单查询

```java
@Service
public class BlacklistQueryService {
    
    @Autowired
    private RcBlackListEntityRepository blackListRepository;
    
    // 查询用户在特定场景下是否在黑名单中
    public boolean isUserInSceneBlacklist(Long sceneId, String userId) {
        RcBlackListEntity blacklist = blackListRepository
            .findByBizTypeIdAndObjectId(sceneId, userId);
        return blacklist != null;
    }
    
    // 获取场景下的所有黑名单用户
    public List<String> getSceneBlacklistUsers(Long sceneId) {
        return blackListRepository.findByBizTypeId(sceneId)
            .stream()
            .map(RcBlackListEntity::getObjectId)
            .collect(Collectors.toList());
    }
    
    // 获取用户在所有场景下的风险状态
    public Map<Long, RcBlackListEntity> getUserRiskAcrossScenes(String userId) {
        return blackListRepository.findByObjectId(userId)
            .stream()
            .collect(Collectors.toMap(
                RcBlackListEntity::getBizTypeId,
                entity -> entity
            ));
    }
}
```

## 🔄 数据迁移建议

如果系统中已有旧的黑名单数据，建议进行数据迁移：

```sql
-- 1. 备份现有数据
CREATE TABLE rc_black_list_backup AS SELECT * FROM rc_black_list;

-- 2. 为现有记录设置默认场景ID（如果适用）
UPDATE rc_black_list 
SET biz_type_id = 1  -- 设置为默认场景ID
WHERE biz_type_id IS NULL;

-- 3. 添加组合唯一索引
ALTER TABLE rc_black_list 
ADD CONSTRAINT uk_biz_object UNIQUE (biz_type_id, object_id);
```

## 📈 监控和维护

### 1. 关键指标监控
- 各场景下的黑名单数量
- 场景间的风险分布
- 重复用户在多场景下的风险状态

### 2. 数据清理策略
```java
// 定期清理过期的黑名单记录
@Scheduled(cron = "0 0 2 * * ?")  // 每天凌晨2点执行
public void cleanupExpiredBlacklist() {
    Date expireDate = DateUtils.addDays(new Date(), -30);  // 30天前
    blackListRepository.deleteByCreateTimeBefore(expireDate);
}
```

## ✅ 修正验证

修正后的系统确保：

1. ✅ **场景隔离**: 不同场景的黑名单完全独立
2. ✅ **数据准确**: 基于场景+对象的精确查找和创建
3. ✅ **业务清晰**: 每个场景的风险管理逻辑清晰
4. ✅ **查询高效**: 支持按场景维度的高效查询
5. ✅ **扩展性好**: 新增场景时不影响现有数据

---

**修正完成！** 🎉

现在黑名单管理完全基于场景化设计，提供了更精确和灵活的风险管理能力。
