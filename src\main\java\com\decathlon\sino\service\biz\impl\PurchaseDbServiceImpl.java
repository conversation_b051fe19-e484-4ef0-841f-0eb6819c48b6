package com.decathlon.sino.service.biz.impl;

import com.decathlon.sino.data.dao.biz.PurchaseEntityRepository;
import com.decathlon.sino.data.entity.biz.PurchaseEntity;
import com.decathlon.sino.kafka.biz.helper.PoslogAnalyzeHelper;
import com.decathlon.sino.model.biz.ItemInfoBpn;
import com.decathlon.sino.service.biz.PurchaseDbService;
import org.nrf_arts.ixretail.v6_0_0.poslog.TransactionDomainSpecific;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PurchaseDbServiceImpl implements PurchaseDbService {

    @Autowired
    private PurchaseEntityRepository purchaseEntityRepository;


    public Double  getSalePrice(TransactionDomainSpecific poslog){
        List<ItemInfoBpn> salePartItems =  PoslogAnalyzeHelper.analyzeItemInfos(poslog, true, false);

        return salePartItems.stream().mapToDouble(ItemInfoBpn::getItemTotalPrice).reduce(0.0, (left, right) -> left + right);

    }

    public Double  getReturnPrice(TransactionDomainSpecific poslog){
        List<ItemInfoBpn> salePartItems =  PoslogAnalyzeHelper.analyzeItemInfos(poslog, false, true);

        return salePartItems.stream().mapToDouble(ItemInfoBpn::getItemTotalPrice).reduce(0.0, (left, right) -> left + right);

    }

    public PurchaseEntity findByTransactionId(String transactionId){
      return  purchaseEntityRepository.findByTransactionId(transactionId);
    }

    @Override
    public List<PurchaseEntity> getLastByCardNumber(String cardNo, Integer limit) {
        return  purchaseEntityRepository.getLastByCardNumber(cardNo,limit);
    }


    @Override
    public Boolean save(PurchaseEntity purchaseEntity) {
        //todo 库里的transaction id的数据的message date应该晚于这一次的message date
        purchaseEntityRepository.save(purchaseEntity);
        return true;
    }
}
