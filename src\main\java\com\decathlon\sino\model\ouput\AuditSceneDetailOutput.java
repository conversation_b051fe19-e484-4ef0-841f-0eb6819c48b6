package com.decathlon.sino.model.ouput;

import java.util.List;

import com.decathlon.sino.model.dto.AuditRuleBaseInfoDto;
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
@JsonNaming(SnakeCaseStrategy.class)
public class AuditSceneDetailOutput extends AuditSceneOutput{
	
	private List<AuditRuleBaseInfoDto> ruleList;

	private Integer rulePointSum;


}
