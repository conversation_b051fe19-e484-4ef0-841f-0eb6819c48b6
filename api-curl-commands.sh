#!/bin/bash

# 审计规则管理系统 - API 测试脚本
# 使用方法: chmod +x api-curl-commands.sh && ./api-curl-commands.sh
# 或者单独执行某个函数，例如: source api-curl-commands.sh && create_scene

# 配置基础URL
BASE_URL="http://localhost:8080"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印分隔线
print_separator() {
    echo -e "${BLUE}================================================${NC}"
}

# 打印标题
print_title() {
    echo -e "${GREEN}$1${NC}"
}

# 打印警告
print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

# 打印错误
print_error() {
    echo -e "${RED}$1${NC}"
}

#=============================================================================
# 1. 场景管理接口
#=============================================================================

# 1.1 创建审计场景
create_scene() {
    print_title "1.1 创建审计场景"
    curl -X POST "${BASE_URL}/scene" \
      -H "Content-Type: application/json" \
      -d '{
        "classification": "风险控制",
        "scene": "用户注册",
        "description": "用户注册场景风险评估",
        "intercept": true,
        "status": true,
        "createBy": "admin"
      }' | jq .
    echo
}

# 1.2 查询所有审计场景
get_all_scenes() {
    print_title "1.2 查询所有审计场景"
    curl -X GET "${BASE_URL}/scene" \
      -H "Accept: application/json" | jq .
    echo
}

# 1.3 获取场景详情
get_scene_detail() {
    local scene_id=${1:-1}
    print_title "1.3 获取场景详情 (scene_id=${scene_id})"
    curl -X GET "${BASE_URL}/scene/detail?scene_id=${scene_id}" \
      -H "Accept: application/json" | jq .
    echo
}

# 1.4 更新审计场景
update_scene() {
    print_title "1.4 更新审计场景"
    curl -X PUT "${BASE_URL}/scene" \
      -H "Content-Type: application/json" \
      -d '{
        "id": 1,
        "classification": "风险控制",
        "scene": "用户登录",
        "description": "用户登录场景风险评估",
        "intercept": false,
        "status": true,
        "updateBy": "admin"
      }' | jq .
    echo
}

#=============================================================================
# 2. 规则参数接口
#=============================================================================

# 2.1 获取所有规则参数
get_all_params() {
    print_title "2.1 获取所有规则参数"
    curl -X GET "${BASE_URL}/params" \
      -H "Accept: application/json" | jq .
    echo
}

#=============================================================================
# 3. 规则管理接口
#=============================================================================

# 3.1 查询规则列表
get_rules() {
    print_title "3.1 查询规则列表"
    
    echo "3.1.1 查询所有规则:"
    curl -X GET "${BASE_URL}/rule" \
      -H "Accept: application/json" | jq .
    echo
    
    echo "3.1.2 按规则名称查询:"
    curl -X GET "${BASE_URL}/rule?rule_name=身份证验证" \
      -H "Accept: application/json" | jq .
    echo
    
    echo "3.1.3 按规则ID查询:"
    curl -X GET "${BASE_URL}/rule?rule_id=1" \
      -H "Accept: application/json" | jq .
    echo
    
    echo "3.1.4 按状态查询:"
    curl -X GET "${BASE_URL}/rule?status=true" \
      -H "Accept: application/json" | jq .
    echo
}

# 3.2 获取规则详情
get_rule_detail() {
    local rule_id=${1:-1}
    print_title "3.2 获取规则详情 (rule_id=${rule_id})"
    curl -X GET "${BASE_URL}/rule/detail?rule_id=${rule_id}" \
      -H "Accept: application/json" | jq .
    echo
}

# 3.3 创建规则
create_rule() {
    print_title "3.3 创建规则"
    curl -X POST "${BASE_URL}/rule" \
      -H "Content-Type: application/json" \
      -d '{
        "ruleName": "身份证号码验证",
        "triggerType": "BEFORE_SAVE",
        "expression": "idCard.length() == 18",
        "description": "验证身份证号码长度",
        "status": true,
        "createBy": "admin",
        "systemParams": [
          {
            "paramName": "idCard",
            "paramType": "String",
            "defaultValue": "",
            "description": "身份证号码",
            "enableConfig": true
          }
        ]
      }' | jq .
    echo
}

# 3.4 更新规则
update_rule() {
    print_title "3.4 更新规则"
    curl -X PUT "${BASE_URL}/rule" \
      -H "Content-Type: application/json" \
      -d '{
        "ruleId": 1,
        "ruleName": "身份证号码验证规则",
        "triggerType": "BEFORE_SAVE",
        "expression": "idCard.length() == 18 && idCard.matches(\"[0-9X]+\")",
        "description": "验证身份证号码格式和长度",
        "status": true,
        "updateBy": "admin",
        "systemParams": [
          {
            "paramName": "idCard",
            "paramType": "String",
            "defaultValue": "",
            "description": "身份证号码",
            "enableConfig": true
          }
        ]
      }' | jq .
    echo
}

#=============================================================================
# 4. 场景规则关联接口
#=============================================================================

# 4.1 绑定场景规则
bind_scene_rule() {
    local rule_id=${1:-1}
    local scene_id=${2:-1}
    print_title "4.1 绑定场景规则 (rule_id=${rule_id}, scene_id=${scene_id})"
    curl -X POST "${BASE_URL}/scene/rule?rule_id=${rule_id}&scene_id=${scene_id}" \
      -H "Accept: application/json" | jq .
    echo
}

# 4.2 解绑规则
unbind_rule() {
    local rule_id=${1:-1}
    print_title "4.2 解绑规则 (rule_id=${rule_id})"
    curl -X DELETE "${BASE_URL}/scene/rule?rule_id=${rule_id}" \
      -H "Accept: application/json" | jq .
    echo
}

# 4.3 更新场景规则（绑定逻辑）
update_scene_rule() {
    print_title "4.3 更新场景规则（绑定逻辑）"
    curl -X PUT "${BASE_URL}/scence/rule" \
      -H "Content-Type: application/json" \
      -d '{
        "ruleId": 1,
        "sceneId": 1,
        "point": 50
      }' | jq .
    echo
}

#=============================================================================
# 5. 黑名单和命中记录接口
#=============================================================================

# 5.1 获取黑名单列表
get_blacklist() {
    local scene_id=${1:-1}
    print_title "5.1 获取黑名单列表 (scene_id=${scene_id})"
    
    echo "5.1.1 基本查询:"
    curl -X GET "${BASE_URL}/blacklist?scene_id=${scene_id}" \
      -H "Accept: application/json" | jq .
    echo
    
    echo "5.1.2 带卡号查询:"
    curl -X GET "${BASE_URL}/blacklist?scene_id=${scene_id}&card_number=123456789" \
      -H "Accept: application/json" | jq .
    echo
    
    echo "5.1.3 带分页参数:"
    curl -X GET "${BASE_URL}/blacklist?scene_id=${scene_id}&page=0&size=10&sort=createTime,desc" \
      -H "Accept: application/json" | jq .
    echo
}

# 5.2 获取规则命中记录（按黑名单ID）
get_hit_records_by_black_id() {
    local black_id=${1:-1}
    print_title "5.2 获取规则命中记录（按黑名单ID=${black_id}）"
    curl -X GET "${BASE_URL}/hitRecords?blackId=${black_id}" \
      -H "Accept: application/json" | jq .
    echo
}

# 5.3 获取规则命中记录（按规则ID）
get_hit_records_by_rule_id() {
    local rule_id=${1:-1}
    print_title "5.3 获取规则命中记录（按规则ID=${rule_id}）"
    
    echo "5.3.1 基本查询:"
    curl -X GET "${BASE_URL}/rule/hitRecords?rule_id=${rule_id}" \
      -H "Accept: application/json" | jq .
    echo
    
    echo "5.3.2 带卡号查询:"
    curl -X GET "${BASE_URL}/rule/hitRecords?rule_id=${rule_id}&card_number=123456789" \
      -H "Accept: application/json" | jq .
    echo
    
    echo "5.3.3 带分页参数:"
    curl -X GET "${BASE_URL}/rule/hitRecords?rule_id=${rule_id}&page=0&size=10&sort=createTime,desc" \
      -H "Accept: application/json" | jq .
    echo
}

#=============================================================================
# 6. 完整测试流程
#=============================================================================

# 完整的测试流程
run_full_test() {
    print_title "6. 完整测试流程"
    print_separator
    
    echo "步骤1: 创建场景"
    create_scene
    
    echo "步骤2: 创建规则"
    create_rule
    
    echo "步骤3: 绑定规则到场景"
    bind_scene_rule 1 1
    
    echo "步骤4: 查询场景详情"
    get_scene_detail 1
    
    echo "步骤5: 查询规则详情"
    get_rule_detail 1
    
    echo "步骤6: 查询黑名单"
    get_blacklist 1
    
    echo "步骤7: 查询命中记录"
    get_hit_records_by_rule_id 1
    
    print_separator
    print_title "完整测试流程执行完毕"
}

#=============================================================================
# 7. 帮助菜单
#=============================================================================

show_help() {
    print_title "审计规则管理系统 API 测试脚本"
    echo
    echo "使用方法:"
    echo "  source api-curl-commands.sh"
    echo "  然后调用相应的函数"
    echo
    echo "可用函数:"
    echo "  场景管理:"
    echo "    create_scene              - 创建审计场景"
    echo "    get_all_scenes           - 查询所有审计场景"
    echo "    get_scene_detail [id]    - 获取场景详情"
    echo "    update_scene             - 更新审计场景"
    echo
    echo "  规则参数:"
    echo "    get_all_params           - 获取所有规则参数"
    echo
    echo "  规则管理:"
    echo "    get_rules                - 查询规则列表"
    echo "    get_rule_detail [id]     - 获取规则详情"
    echo "    create_rule              - 创建规则"
    echo "    update_rule              - 更新规则"
    echo
    echo "  场景规则关联:"
    echo "    bind_scene_rule [rule_id] [scene_id] - 绑定场景规则"
    echo "    unbind_rule [rule_id]    - 解绑规则"
    echo "    update_scene_rule        - 更新场景规则"
    echo
    echo "  黑名单和命中记录:"
    echo "    get_blacklist [scene_id] - 获取黑名单列表"
    echo "    get_hit_records_by_black_id [black_id] - 按黑名单ID获取命中记录"
    echo "    get_hit_records_by_rule_id [rule_id]   - 按规则ID获取命中记录"
    echo
    echo "  完整测试:"
    echo "    run_full_test            - 运行完整测试流程"
    echo
    echo "  其他:"
    echo "    show_help                - 显示此帮助信息"
    echo
    echo "示例:"
    echo "  get_scene_detail 1       - 获取ID为1的场景详情"
    echo "  bind_scene_rule 1 2      - 将规则1绑定到场景2"
    echo
    print_warning "注意: 请确保服务运行在 ${BASE_URL}"
    print_warning "注意: 需要安装 jq 工具来格式化JSON输出"
}

#=============================================================================
# 8. 原始 curl 命令参考
#=============================================================================

show_raw_curls() {
    print_title "原始 curl 命令参考"
    echo
    cat << 'EOF'
# 场景管理
curl -X POST "http://localhost:8080/scene" -H "Content-Type: application/json" -d '{"classification": "风险控制", "scene": "用户注册", "description": "用户注册场景风险评估", "intercept": true, "status": true, "createBy": "admin"}'
curl -X GET "http://localhost:8080/scene" -H "Accept: application/json"
curl -X GET "http://localhost:8080/scene/detail?scene_id=1" -H "Accept: application/json"
curl -X PUT "http://localhost:8080/scene" -H "Content-Type: application/json" -d '{"id": 1, "classification": "风险控制", "scene": "用户登录", "description": "用户登录场景风险评估", "intercept": false, "status": true, "updateBy": "admin"}'

# 规则参数
curl -X GET "http://localhost:8080/params" -H "Accept: application/json"

# 规则管理
curl -X GET "http://localhost:8080/rule" -H "Accept: application/json"
curl -X GET "http://localhost:8080/rule?rule_name=身份证验证" -H "Accept: application/json"
curl -X GET "http://localhost:8080/rule?rule_id=1" -H "Accept: application/json"
curl -X GET "http://localhost:8080/rule?status=true" -H "Accept: application/json"
curl -X GET "http://localhost:8080/rule/detail?rule_id=1" -H "Accept: application/json"
curl -X POST "http://localhost:8080/rule" -H "Content-Type: application/json" -d '{"ruleName": "身份证号码验证", "triggerType": "BEFORE_SAVE", "expression": "idCard.length() == 18", "description": "验证身份证号码长度", "status": true, "createBy": "admin", "systemParams": [{"paramName": "idCard", "paramType": "String", "defaultValue": "", "description": "身份证号码", "enableConfig": true}]}'
curl -X PUT "http://localhost:8080/rule" -H "Content-Type: application/json" -d '{"ruleId": 1, "ruleName": "身份证号码验证规则", "triggerType": "BEFORE_SAVE", "expression": "idCard.length() == 18 && idCard.matches(\"[0-9X]+\")", "description": "验证身份证号码格式和长度", "status": true, "updateBy": "admin"}'

# 场景规则关联
curl -X POST "http://localhost:8080/scene/rule?rule_id=1&scene_id=1" -H "Accept: application/json"
curl -X DELETE "http://localhost:8080/scene/rule?rule_id=1" -H "Accept: application/json"
curl -X PUT "http://localhost:8080/scence/rule" -H "Content-Type: application/json" -d '{"ruleId": 1, "sceneId": 1, "point": 50}'

# 黑名单和命中记录
curl -X GET "http://localhost:8080/blacklist?scene_id=1" -H "Accept: application/json"
curl -X GET "http://localhost:8080/blacklist?scene_id=1&card_number=123456789" -H "Accept: application/json"
curl -X GET "http://localhost:8080/blacklist?scene_id=1&page=0&size=10&sort=createTime,desc" -H "Accept: application/json"
curl -X GET "http://localhost:8080/hitRecords?blackId=1" -H "Accept: application/json"
curl -X GET "http://localhost:8080/rule/hitRecords?rule_id=1" -H "Accept: application/json"
curl -X GET "http://localhost:8080/rule/hitRecords?rule_id=1&card_number=123456789" -H "Accept: application/json"
curl -X GET "http://localhost:8080/rule/hitRecords?rule_id=1&page=0&size=10&sort=createTime,desc" -H "Accept: application/json"
EOF
}

#=============================================================================
# 9. 环境检查
#=============================================================================

check_environment() {
    print_title "环境检查"

    # 检查 curl
    if ! command -v curl &> /dev/null; then
        print_error "curl 未安装，请先安装 curl"
        return 1
    else
        print_title "✓ curl 已安装"
    fi

    # 检查 jq
    if ! command -v jq &> /dev/null; then
        print_warning "⚠ jq 未安装，JSON输出将不会格式化"
        print_warning "  安装命令: sudo apt-get install jq (Ubuntu/Debian)"
        print_warning "  安装命令: brew install jq (macOS)"
    else
        print_title "✓ jq 已安装"
    fi

    # 检查服务是否可达
    print_title "检查服务连接..."
    if curl -s --connect-timeout 5 "${BASE_URL}/actuator/health" > /dev/null 2>&1 || \
       curl -s --connect-timeout 5 "${BASE_URL}" > /dev/null 2>&1; then
        print_title "✓ 服务连接正常"
    else
        print_warning "⚠ 无法连接到服务 ${BASE_URL}"
        print_warning "  请确保服务正在运行"
    fi
    echo
}

#=============================================================================
# 10. 快捷命令
#=============================================================================

# 快速测试连接
test_connection() {
    print_title "测试服务连接"
    curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n连接时间: %{time_connect}s\n总时间: %{time_total}s\n" "${BASE_URL}/scene"
}

# 设置不同的环境
set_env() {
    case $1 in
        "local")
            BASE_URL="http://localhost:8080"
            ;;
        "dev")
            BASE_URL="http://dev-server:8080"
            ;;
        "test")
            BASE_URL="http://test-server:8080"
            ;;
        "prod")
            BASE_URL="http://prod-server:8080"
            ;;
        *)
            BASE_URL="$1"
            ;;
    esac
    print_title "环境已设置为: ${BASE_URL}"
}

# 如果直接执行脚本，显示帮助信息
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_environment
    show_help
fi
