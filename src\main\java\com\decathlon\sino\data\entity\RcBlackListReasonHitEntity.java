package com.decathlon.sino.data.entity;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.decathlon.sino.data.entity.basic.IdEntity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import java.math.BigDecimal;

/**
 * 黑名单命中记录实体类
 * 该实体类用于记录黑名单规则的命中情况
 * 同一个objectId 在同一个blackListId 上可能会有多个命中记录,分别表示不同的规则命中情况
 * 用于后续的黑名单管理和查询
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_black_list_reason_hit")
public class RcBlackListReasonHitEntity extends IdEntity {

	private static final long serialVersionUID = 1L;
	
	/**
	 * 生成黑名单命中记录时的规则ID
	 */
	private Long blackListId;
	/**
	 * 命中规则的ID
	 * 例如：规则ID
	 * 用于标识命中哪个具体的规则
	 */
	private Long ruleId;
	/**
	 * 命中规则的对象
	 * 例如：用户ID、订单ID等
	 * 用于标识命中规则的具体对象
	 */
	private String objectId;
	/**
	 * 命中规则的分数
	 */
	private BigDecimal point;
	/**
	 * 命中ruleId的上下文
	 */
	private String context;
	private Date createTime;
	private Date updateTime;
	private Date refreshTime;

}
