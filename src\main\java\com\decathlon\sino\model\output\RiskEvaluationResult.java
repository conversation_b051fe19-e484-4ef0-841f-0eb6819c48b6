package com.decathlon.sino.model.output;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 风险评估结果 - 审计评估模式的返回结果
 * 
 * 用于封装先进行规则评估再返回结果的评估过程信息
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RiskEvaluationResult {
    
    /**
     * 场景ID
     */
    private Long sceneId;
    
    /**
     * 对象ID
     */
    private String objectId;
    
    /**
     * 评估时间
     */
    private Date evaluationTime;
    
    /**
     * 是否已记录到数据库
     */
    private Boolean recorded;
    
    /**
     * 本次评估命中规则数量
     */
    private Integer hitRuleCount;
    
    /**
     * 是否有新的命中
     */
    private Boolean hasNewHits;
    
    /**
     * 本次评估的新命中记录
     */
    private List<EvaluationHit> newHits;
    
    /**
     * 当前是否在黑名单中
     */
    private Boolean inBlacklist;
    
    /**
     * 当前总分数
     */
    private BigDecimal currentTotalScore;
    
    /**
     * 当前风险等级 (LOW/MEDIUM/HIGH)
     */
    private String currentRiskLevel;

    /**
     * 是否拦截
     */
    private Boolean blocked;
    
    /**
     * 评估命中详情
     */
    @Data
    public static class EvaluationHit {
        
        /**
         * 规则ID
         */
        private Long ruleId;
        
        /**
         * 规则表达式
         */
        private String ruleExpression;
        
        /**
         * 命中时间
         */
        private Date hitTime;
        
        /**
         * 规则分数
         */
        private BigDecimal point;
        
        /**
         * 命中原因
         */
        private String reason;
        
        /**
         * 命中上下文
         */
        private String context;
    }
}
