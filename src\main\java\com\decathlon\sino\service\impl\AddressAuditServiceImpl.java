package com.decathlon.sino.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.decathlon.sino.common.exception.ServiceException;
import com.decathlon.sino.component.RiskEngineComponent;
import com.decathlon.sino.data.dao.RcBizScenceEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListReasonHitEntityRepository;
import com.decathlon.sino.data.dao.RcBlacklistGlobalEntityRepository;
import com.decathlon.sino.model.output.RiskEvaluationResult;
import com.decathlon.sino.model.output.RiskQueryResult;
import com.decathlon.sino.service.AuditService;
import com.decathlon.sino.service.impl.helper.AddressRiskHelper;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 * 地址审计服务实现
 *
 * 专门处理地址相关的风险审计，包括：
 * - 收货地址风险评估
 * - 地址异常检测
 * - 地址黑名单管理
 *
 * 继承自 AduitProcessService，复用基础的风险查询和评估功能
 */
@Service
@Slf4j
public class AddressAuditServiceImpl extends AduitProcessService implements AuditService {

	/**
	 * 构造函数 - 注入所需的依赖
	 */
	public AddressAuditServiceImpl(
			RiskEngineComponent riskEngineComponent,
			RcBlackListEntityRepository rcBlackListEntityRepository,
			RcBlackListReasonHitEntityRepository rcBlackListReasonHitEntityRepository,
			RcBizScenceEntityRepository rcBizScenceEntityRepository,
			RcBlacklistGlobalEntityRepository rcGlobalBlackListEntityRepository) {
		super(riskEngineComponent, rcBlackListEntityRepository, rcBlackListReasonHitEntityRepository,
			  rcBizScenceEntityRepository, rcGlobalBlackListEntityRepository);
	}

	/**
	 * 地址风险检查
	 *
	 * @param sceneId 场景ID
	 * @param objectId 对象ID（如用户ID、订单ID等）
	 * @param eventData 事件数据（包含地址信息）
	 * @param operator 操作人
	 * @param enableAudit 是否启用审计模式
	 * @return 风险查询结果
	 */
	@Override
	public RiskQueryResult checkRisk(Long sceneId, String objectId, JSONObject eventData, String operator, Boolean enableAudit) {
		log.info("开始地址风险检查: sceneId={}, objectId={}, enableAudit={}", sceneId, objectId, enableAudit);

		try {
			if (Boolean.TRUE.equals(enableAudit)) {
				// 审计模式：先评估再返回结果
				log.debug("使用审计模式进行地址风险评估");
				RiskEvaluationResult evaluationResult = this.auditRisk(sceneId, objectId, eventData, operator, enableAudit);

				// 将评估结果转换为查询结果格式
				return convertEvaluationToQueryResult(evaluationResult);
			} else {
				// 直接查询模式：快速查询现有风险状态
				log.debug("使用直接查询模式检查地址风险");
				return super.checkRisk(sceneId, objectId);
			}
		} catch (Exception e) {
			log.error("地址风险检查失败: sceneId={}, objectId={}", sceneId, objectId, e);
			throw new ServiceException("地址风险检查失败", e);
		}
	}

	/**
	 * 地址风险审计评估
	 *
	 * @param sceneId 场景ID
	 * @param objectId 对象ID
	 * @param eventData 事件数据（包含地址信息）
	 * @param operator 操作人
	 * @param enableRecord 是否记录评估结果
	 * @return 风险评估结果
	 */
	@Override
	public RiskEvaluationResult auditRisk(Long sceneId, String objectId, JSONObject eventData, String operator, Boolean enableRecord) {
		log.info("开始地址风险审计评估: sceneId={}, objectId={}, enableRecord={}", sceneId, objectId, enableRecord);

		try {
			// 1. 提取地址相关的上下文信息
			Map<String, Object> addressContext = extractAddressContext(eventData);
			log.debug("地址上下文提取完成: {}", addressContext);

			// 2. 调用父类的审计评估方法
			RiskEvaluationResult result = super.auditRisk(sceneId, objectId, addressContext, operator, enableRecord);

			// 3. 增强地址特定的风险信息
			enhanceAddressRiskInfo(result, eventData);

			log.info("地址风险审计评估完成: sceneId={}, objectId={}, hitCount={}",
					sceneId, objectId, result.getHitRuleCount());

			return result;
		} catch (Exception e) {
			log.error("地址风险审计评估失败: sceneId={}, objectId={}", sceneId, objectId, e);
			throw new ServiceException("地址风险审计评估失败", e);
		}
	}

	/**
	 * 提取地址相关的上下文信息
	 *
	 * @param eventData 事件数据
	 * @return 地址上下文信息
	 */
	private Map<String, Object> extractAddressContext(JSONObject eventData) {
		if (eventData == null) {
			log.warn("事件数据为空，使用空的地址上下文");
			return Map.of();
		}

		try {
			// 使用 AddressRiskHelper 提取地址上下文
			Map<String, Object> context = AddressRiskHelper.getContext(eventData);
			log.debug("地址上下文提取成功: keys={}", context.keySet());
			return context;
		} catch (Exception e) {
			log.error("地址上下文提取失败", e);
			return Map.of();
		}
	}

	/**
	 * 增强地址特定的风险信息
	 *
	 * @param result 风险评估结果
	 * @param eventData 原始事件数据
	 */
	private void enhanceAddressRiskInfo(RiskEvaluationResult result, JSONObject eventData) {
		// 这里可以添加地址特定的风险信息增强逻辑
		// 例如：地址风险等级、地址类型、地理位置风险等

		// 示例：添加地址类型信息到结果中
		if (eventData != null && eventData.containsKey("addressType")) {
			// 可以在结果中添加额外的地址风险信息
			log.debug("地址类型: {}", eventData.getStr("addressType"));
		}
	}

	/**
	 * 将评估结果转换为查询结果格式
	 *
	 * @param evaluationResult 评估结果
	 * @return 查询结果
	 */
	private RiskQueryResult convertEvaluationToQueryResult(RiskEvaluationResult evaluationResult) {
		RiskQueryResult queryResult = new RiskQueryResult();
		queryResult.setSceneId(evaluationResult.getSceneId());
		queryResult.setObjectId(evaluationResult.getObjectId());
		queryResult.setInBlacklist(evaluationResult.getInBlacklist());
		queryResult.setRiskLevel(evaluationResult.getCurrentRiskLevel());
		queryResult.setTotalScore(evaluationResult.getCurrentTotalScore());
		queryResult.setHitRuleCount(evaluationResult.getHitRuleCount());
		queryResult.setBlocked(evaluationResult.getBlocked());
		queryResult.setQueryTime(evaluationResult.getEvaluationTime());

		// 设置命中记录为空（审计模式下的新命中记录不包含在查询结果中）
		queryResult.setHitRecords(java.util.Collections.emptyList());
		queryResult.setLastHitTime(null);

		return queryResult;
	}
}
