package com.decathlon.sino.kafka.biz.helper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.decathlon.sino.component.RiskEngineComponent;
import com.decathlon.sino.data.dao.RcBlackListRuleEntityRepository;
import com.decathlon.sino.data.dao.RcScenceRuleEntityRepository;
import com.decathlon.sino.data.dao.biz.AccountInfoEntityRepository;
import com.decathlon.sino.data.entity.RcBlackListRuleEntity;
import com.decathlon.sino.data.entity.RcScenceRuleEntity;
import com.decathlon.sino.data.entity.biz.AccountInfoEntity;
import com.decathlon.sino.kafka.biz.helper.dto.AuditMessageDto;
import com.decathlon.sino.service.impl.AuditDataManagerServiceImpl;

import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@AllArgsConstructor
public class MemberListenerProcessHelper {
	
	private final AccountInfoEntityRepository accountInfoEntityRepository;
	private final AuditDataManagerServiceImpl auditBackOfficeServiceImpl;
	private final RcBlackListRuleEntityRepository rcBlackListRuleEntityRepository;
	private final RcScenceRuleEntityRepository rcScenceRuleEntityRepository;
	private final RiskEngineComponent riskEngineComponent;
	private static final String TRIGGER_TYPE = "MEMBER_INFO_CHANGE";
	
	@Async
	public void process(ConsumerRecord<String, Object> records) {
		try {
			log.info("MemberListenerProcessHelper Processing message: {}", records.value());
			AccountInfoEntity accountPointEntity = transform((String)records.value());
			accountInfoEntityRepository.save(accountPointEntity);
			Map<String, Object> context = getAuditMessageMap(accountPointEntity.getCardNumber());
			// Trigger risk engine evaluation for point changes
			List<RcBlackListRuleEntity> rules = rcBlackListRuleEntityRepository.findByTriggerType(TRIGGER_TYPE);
			for (RcBlackListRuleEntity rcBlackListRuleEntity : rules) {
				List<RcScenceRuleEntity> rcScenceRuleEntities = rcScenceRuleEntityRepository.findByRuleId(rcBlackListRuleEntity.getId());
				for (RcScenceRuleEntity rcScenceRuleEntity : rcScenceRuleEntities) {
					riskEngineComponent.evaluateRules(rcScenceRuleEntity.getSceneId(), accountPointEntity.getCardNumber(), context, "SYSTEM_AUTO", true);
				}
			}
		} catch (Exception e) {
			log.error("Error processing message: {}", e.getMessage(), e);
			throw e;
		}		
	}

	/**
	 * Transform the message value to AccountInfoEntity
	 * @param value
	 * @return AccountInfoEntity
	 */
	private AccountInfoEntity transform(String value) {
		AuditMessageDto auditMessageDto = JSONUtil.toBean(value, AuditMessageDto.class);
		AccountInfoEntity accountInfoEntity = accountInfoEntityRepository.findByPersonId(auditMessageDto.getPersonId());
		if(accountInfoEntity == null) {
			accountInfoEntity = new AccountInfoEntity();
			accountInfoEntity.setPersonId(auditMessageDto.getPersonId());
		}
		if("INFO_AUDIT".equals(auditMessageDto.getType())) {
			// Update personal information
			accountInfoEntity.setCardNumber(auditMessageDto.getCardNumber());
			accountInfoEntity.setMobile(auditMessageDto.getMobile());
			accountInfoEntity.setName(auditMessageDto.getName());
			accountInfoEntity.setGender(auditMessageDto.getGender());
			accountInfoEntity.setRegisterClientId(auditMessageDto.getRegisterClientId());
			accountInfoEntity.setDeleteDate(auditMessageDto.getDeleteDate());
			accountInfoEntity.setRegisterDate(auditMessageDto.getRegisterDate());
			accountInfoEntity.setDeleteReason(auditMessageDto.getDeleteReason());
			accountInfoEntity.setBirthDate(auditMessageDto.getBirthDate());
		}else if("STORE_AUDIT".equals(auditMessageDto.getType())) {
			// Update store information
			accountInfoEntity.setRegisterStoreNumber(auditMessageDto.getRegisterStoreNumber());
			accountInfoEntity.setRegisterStoreName(auditMessageDto.getRegisterStoreName());
			accountInfoEntity.setUsualStoreNumber(auditMessageDto.getUsualStoreNumber());
			accountInfoEntity.setUsualStoreName(auditMessageDto.getUsualStoreName());
			accountInfoEntity.setDeleteDate(auditMessageDto.getDeleteDate());
			accountInfoEntity.setRegisterDate(auditMessageDto.getRegisterDate());
			accountInfoEntity.setDeleteReason(auditMessageDto.getDeleteReason());
			
		}else if("ADDRESS_AUDIT".equals(auditMessageDto.getType())) {
			// Update address number 
			accountInfoEntity.setAddressNumber(auditMessageDto.getAddressNumber());
		}
		
		return accountInfoEntity;
	}
	
	public Map<String, Object> getAuditMessageMap(String cardNumber) {
		HashMap<String, Object> auditMessage = new HashMap<>();
		// 获取有积分变更用户的门店信息
		AccountInfoEntity accountInfoEntity = accountInfoEntityRepository.findByCardNumber(cardNumber);
		if(accountInfoEntity == null) {
			AuditMessageDto infoBody = auditBackOfficeServiceImpl.queryInfo("cardNumber", cardNumber);
			if(infoBody == null) {
				log.error("No account found for card number: {}", cardNumber);
				return null; 
			}else {
				accountInfoEntity = new AccountInfoEntity();
				accountInfoEntity.setCardNumber(infoBody.getCardNumber());
				accountInfoEntity.setMobile(infoBody.getMobile());
				accountInfoEntity.setName(infoBody.getName());
				accountInfoEntity.setGender(infoBody.getGender());
				accountInfoEntity.setRegisterClientId(infoBody.getRegisterClientId());
				accountInfoEntity.setDeleteDate(infoBody.getDeleteDate());
				accountInfoEntity.setRegisterDate(infoBody.getRegisterDate());
				accountInfoEntity.setDeleteReason(infoBody.getDeleteReason());
				accountInfoEntity.setBirthDate(infoBody.getBirthDate());
				AuditMessageDto storeBody = auditBackOfficeServiceImpl.queryStore(accountInfoEntity.getPersonId());
				if(storeBody==null) {
					log.error("No account found for card number: {}", cardNumber);
					return null;  
				}else {
					accountInfoEntity.setRegisterStoreNumber(storeBody.getRegisterStoreNumber());
					accountInfoEntity.setRegisterStoreName(storeBody.getRegisterStoreName());
					accountInfoEntity.setUsualStoreNumber(storeBody.getUsualStoreNumber());
					accountInfoEntity.setUsualStoreName(storeBody.getUsualStoreName());
					accountInfoEntity.setDeleteDate(storeBody.getDeleteDate());
					accountInfoEntity.setRegisterDate(storeBody.getRegisterDate());
					accountInfoEntity.setDeleteReason(storeBody.getDeleteReason());
					
				}
				accountInfoEntityRepository.save(accountInfoEntity);
			}
		}
		auditMessage.put("store", accountInfoEntity.getRegisterStoreNumber());
		// 获取有积分变更用户的注册时间
		auditMessage.put("registerDate", accountInfoEntity.getRegisterDate());
		// 获取有积分变更用户的姓名
		auditMessage.put("name", accountInfoEntity.getName());
		// 获取有积分变更用户的性别
		auditMessage.put("gender", accountInfoEntity.getGender());
		// 获取有积分变更用户的生日
		auditMessage.put("birthday", accountInfoEntity.getBirthDate());
		return auditMessage;
	}
	
	
	

}
