package com.decathlon.sino.model.ouput;

import java.util.Date;

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

@Data
@JsonNaming(SnakeCaseStrategy.class)
public class AuditSceneOutput {
	
	private Long sceneId;
	private String classification;
	private String sceneName;
	private String sceneDesc;
	
	// 是否拦截
	private Boolean interceptStatus;
	// 启用状态
	private Boolean enableStatus;
	private Date updateTime;
	private String createBy;

}
