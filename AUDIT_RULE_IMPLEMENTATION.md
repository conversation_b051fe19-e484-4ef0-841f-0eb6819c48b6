# 审计规则后台管理功能实现文档

## 概述

本文档描述了为Sino-Aud风险审核系统实现的后台管理功能，主要包括审计场景管理、规则管理、黑名单查询和命中记录查询等功能。

## 实现的功能

### 1. 审计场景管理

#### 1.1 创建审计场景
- **接口**: `POST /scene`
- **功能**: 创建新的审计场景
- **实现**: 在`rc_biz_type`表中创建新记录

#### 1.2 查询所有场景
- **接口**: `GET /scene`
- **功能**: 获取所有审计场景列表
- **实现**: 查询`rc_biz_type`表，按更新时间倒序排列

#### 1.3 获取场景详情
- **接口**: `GET /scene/detail?scene_id={sceneId}`
- **功能**: 获取指定场景的详细信息，包括关联的规则列表
- **实现**: 联合查询`rc_biz_type`、`rc_scene_rule_relation`和`rc_black_list_rule`表

#### 1.4 更新场景
- **接口**: `PUT /scene`
- **功能**: 更新现有审计场景信息
- **实现**: 更新`rc_biz_type`表中的记录

### 2. 规则管理

#### 2.1 创建规则
- **接口**: `POST /rule`
- **功能**: 创建新的审计规则
- **实现**: 
  - 在`rc_black_list_rule`表中创建规则记录
  - 在`rc_black_list_rule_param`表中创建关联的参数记录

#### 2.2 查询规则列表
- **接口**: `GET /rule?rule_name={name}&rule_id={id}&status={status}`
- **功能**: 根据条件查询规则列表
- **实现**: 使用QueryDSL动态构建查询条件

#### 2.3 获取规则详情
- **接口**: `GET /rule/detail?rule_id={ruleId}`
- **功能**: 获取指定规则的详细信息，包括参数列表
- **实现**: 联合查询`rc_black_list_rule`和`rc_black_list_rule_param`表

#### 2.4 更新规则
- **接口**: `PUT /rule`
- **功能**: 更新现有规则信息
- **实现**: 
  - 更新`rc_black_list_rule`表中的规则记录
  - 删除并重新创建`rc_black_list_rule_param`表中的参数记录

#### 2.5 删除规则
- **接口**: `DELETE /scene/rule?rule_id={ruleId}`
- **功能**: 删除指定规则及其关联数据
- **实现**: 
  - 删除`rc_black_list_rule_param`表中的参数记录
  - 删除`rc_scene_rule_relation`表中的场景关联记录
  - 删除`rc_black_list_rule`表中的规则记录

### 3. 场景规则关联管理

#### 3.1 绑定规则到场景
- **接口**: `POST /scene/rule?rule_id={ruleId}&scene_id={sceneId}`
- **功能**: 将规则绑定到指定场景
- **实现**: 在`rc_scene_rule_relation`表中创建关联记录

#### 3.2 更新场景规则配置
- **接口**: `PUT /scence/rule`
- **功能**: 更新场景规则的配置（如分数权重）
- **实现**: 更新`rc_scene_rule_relation`表中的记录

### 4. 黑名单和命中记录查询

#### 4.1 查询黑名单列表
- **接口**: `GET /blacklist?scene_id={sceneId}&card_number={cardNumber}`
- **功能**: 分页查询黑名单记录
- **实现**: 使用QueryDSL查询`rc_black_list`表

#### 4.2 查询规则命中记录
- **接口**: `GET /rule/hitRecords?rule_id={ruleId}&card_number={cardNumber}`
- **功能**: 分页查询规则命中记录
- **实现**: 联合查询多个表获取完整的命中记录信息

#### 4.3 根据黑名单ID查询命中记录
- **接口**: `GET /hitRecords?blackId={blackId}`
- **功能**: 查询指定黑名单的所有命中记录
- **实现**: 查询`rc_black_list_reason_hit`表

### 5. 参数管理

#### 5.1 获取所有规则参数
- **接口**: `GET /params`
- **功能**: 获取系统中所有的规则参数定义
- **实现**: 查询`rc_black_list_rule_param`表

## 数据库表结构

### 主要数据表

1. **rc_biz_type** - 业务场景表
   - id: 主键
   - classification: 分类
   - scene: 场景名称
   - description: 描述
   - intercept: 是否拦截
   - status: 启用状态

2. **rc_black_list_rule** - 规则表
   - id: 主键
   - rule_name: 规则名称
   - trigger_type: 触发类型
   - expression: 表达式
   - description: 描述
   - status: 状态

3. **rc_black_list_rule_param** - 规则参数表
   - id: 主键
   - rule_id: 规则ID
   - param_name: 参数名称
   - param_type: 参数类型
   - default_value: 默认值
   - enable_config: 是否可配置

4. **rc_scene_rule_relation** - 场景规则关联表
   - id: 主键
   - scene_id: 场景ID
   - rule_id: 规则ID
   - point: 分数权重

5. **rc_black_list** - 黑名单表
   - id: 主键
   - biz_type_id: 业务类型ID
   - object_id: 对象ID
   - final_score: 最终分数
   - risk_level: 风险等级

6. **rc_black_list_reason_hit** - 命中记录表
   - id: 主键
   - black_list_id: 黑名单ID
   - rule_id: 规则ID
   - object_id: 对象ID
   - point: 命中分数
   - context: 上下文信息

## 技术实现特点

### 1. 使用QueryDSL进行动态查询
- 支持灵活的查询条件组合
- 类型安全的查询构建
- 良好的性能和可维护性

### 2. 事务管理
- 关键操作使用`@Transactional`注解
- 确保数据一致性

### 3. 参数验证
- 使用Bean Validation进行参数校验
- 自定义异常处理

### 4. 分页查询
- 统一的分页查询实现
- 支持排序和条件过滤

### 5. DTO转换
- 清晰的输入输出模型定义
- 使用QueryDSL Projections进行高效的数据转换

## 测试覆盖

### 单元测试
- Service层业务逻辑测试
- 使用Mockito进行依赖模拟
- 覆盖正常流程和异常情况

### 集成测试
- Controller层API测试
- 使用MockMvc进行HTTP请求测试
- 验证JSON响应格式

## 使用示例

### 创建场景
```bash
curl -X POST http://localhost:8282/portal/api/audit/scene \
  -H "Content-Type: application/json" \
  -d '{
    "classification": "POINT",
    "scene_name": "积分场景",
    "scene_desc": "积分相关的风险检测场景",
    "intercept_status": true,
    "enable_status": true
  }'
```

### 创建规则
```bash
curl -X POST http://localhost:8282/portal/api/audit/rule \
  -H "Content-Type: application/json" \
  -d '{
    "rule_name": "高额积分消费规则",
    "rule_desc": "检测单次消费大量积分的行为",
    "trigger_type": "POINT",
    "expression": "pointChange < -10000",
    "status": true,
    "create_by": "admin"
  }'
```

### 查询场景列表
```bash
curl -X GET http://localhost:8282/portal/api/audit/scene
```

### 查询规则列表
```bash
curl -X GET "http://localhost:8282/portal/api/audit/rule?status=true"
```

## 注意事项

1. **数据一致性**: 删除规则时会级联删除相关的参数和场景关联记录
2. **权限控制**: 当前实现未包含权限验证，生产环境需要添加认证授权
3. **性能优化**: 大数据量查询时建议添加适当的数据库索引
4. **错误处理**: 建议完善全局异常处理机制
5. **日志记录**: 关键操作已添加日志记录，便于问题排查

## 后续优化建议

1. 添加缓存机制提升查询性能
2. 实现规则表达式的语法验证
3. 添加操作审计日志
4. 实现规则的版本管理
5. 添加规则测试和模拟功能
