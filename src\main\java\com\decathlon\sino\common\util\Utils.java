package com.decathlon.sino.common.util;

import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.SpelParserConfiguration;

import java.util.List;

import org.springframework.expression.Expression;

public class Utils {
    public static String reverse(String input) {
        return new StringBuilder(input).reverse().toString();
    }
    
    //sum
    public static Integer sum(List<Integer> list) {
		Integer sum = 0;
		for (Integer i : list) {
			sum += i;
		}
		return sum;
	}
    
    public static void main(String[] args) throws NoSuchMethodException, SecurityException {
		
    	
    	Long.valueOf(null);
    	
	}
}


