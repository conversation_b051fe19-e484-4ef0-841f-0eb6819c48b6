package com.decathlon.sino.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 规则命中结果
 * 
 * 用于封装规则评估的命中结果，包含命中的规则信息、
 * 对象信息、命中时间、上下文参数等详细信息
 */
@Data
public class RuleHitResult {
    
    /**
     * 命中的规则ID
     */
    private Long ruleId;
    
    /**
     * 被评估的对象ID（如用户ID、订单ID等）
     */
    private String objectId;
    
    /**
     * 命中时间
     */
    private Date hitTime;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 规则表达式
     */
    private String ruleExpression;
    
    /**
     * 规则权重分数
     */
    private BigDecimal point;
    
    /**
     * 命中时的上下文参数（JSON格式）
     */
    private String context;
    
    /**
     * 命中原因描述
     */
    private String reason;
    
    private Integer deferMinutes;
    /**
     * 黑名单记录ID（记录到数据库后设置）
     */
    private Long blackListId;
    
    /**
     * 是否已记录到数据库
     */
    private Boolean recorded = false;
}
