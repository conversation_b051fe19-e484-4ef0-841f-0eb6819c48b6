package com.decathlon.sino.data.entity.biz;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.decathlon.sino.data.entity.basic.IdEntity;
import com.google.gson.JsonArray;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "biz_purchase")
public class PurchaseEntity extends IdEntity {

    private static final long serialVersionUID = 1L;

    private String cardNo;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private Date createdAt = new Date();

    @UpdateTimestamp
    @Column(name = "updated_at")
    private Date updatedAt;

    @Column(unique = true)
    private String transactionId;

    private String originTransactionId;

    @Column(name="sale_detail")
    private String saleDetail;

    @Column(name="return_detail")
    private String returnDetail;

    private Date transactionDate;

    private LocalDateTime purchaseDate;

    private Double salePrice;

    private Double returnPrice;

    private Double totalPrice;

    private String status;

    private String channel;






}
