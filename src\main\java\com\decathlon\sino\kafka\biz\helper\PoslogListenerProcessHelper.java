package com.decathlon.sino.kafka.biz.helper;

import cn.hutool.json.JSONUtil;
import com.decathlon.ibuy.Poslog;
import com.decathlon.sino.common.util.DateUtil;
import com.decathlon.sino.component.RiskEngineComponent;
import com.decathlon.sino.data.dao.RcBlackListRuleEntityRepository;
import com.decathlon.sino.data.dao.RcScenceRuleEntityRepository;
import com.decathlon.sino.data.entity.RcBlackListRuleEntity;
import com.decathlon.sino.data.entity.RcScenceRuleEntity;
import com.decathlon.sino.data.entity.biz.PurchaseEntity;
import com.decathlon.sino.model.biz.ItemInfoBpn;
import com.decathlon.sino.service.biz.PurchaseDbService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.nrf_arts.ixretail.v6_0_0.poslog.POSLogBase;
import org.nrf_arts.ixretail.v6_0_0.poslog.TransactionDomainSpecific;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.xml.bind.JAXBException;
import java.util.*;

import static com.decathlon.sino.kafka.biz.helper.PoslogAnalyzeHelper.getPoslogBase;

@Component
@Slf4j
public class PoslogListenerProcessHelper {



	@Autowired
	private RcBlackListRuleEntityRepository rcBlackListRuleEntityRepository;
	
	@Autowired
	private RcScenceRuleEntityRepository rcScenceRuleEntityRepository;

    @Autowired
	private PurchaseDbService purchaseDbService;

	@Autowired
	private RiskEngineComponent riskEngineComponent;

	@Value("${purchase.audit-limit:30}")
	private Integer purchaseAudit;
	
	private static final String TRIGGER_TYPE = "PURCHASE_CHANGE"; 


	@Async
	public void process(ConsumerRecord<String, Object> records) throws JAXBException {
			log.info("Processing message: {}", records.value());
			Poslog poslog = (Poslog)records.value();
			String xmlContent = poslog.getPayload().getDataXml();
			POSLogBase poslogBase = getPoslogBase(xmlContent);

			// acquire transaction
			final TransactionDomainSpecific transactionDomainSpecific = poslogBase.getTransaction()
					.get(0);
			PurchaseEntity purchase = transform(transactionDomainSpecific);
			Boolean success = purchaseDbService.save(purchase);
		    log.info("process save purchase successfully: {}", success);
		    audit(purchase.getCardNo());


	}


	public void audit(String cardNumber) {

		List<PurchaseEntity> list = purchaseDbService.getLastByCardNumber(cardNumber, purchaseAudit);
		list.forEach(purchaseEntity -> {
			log.info("last purchase: {}", purchaseEntity);

			String saleDetail = purchaseEntity.getSaleDetail();
			//没有购买数据，只有退货数据，比如线下
			if(StringUtils.isEmpty(saleDetail)){
				return;
			}

			JSONUtil.toList(saleDetail, ItemInfoBpn.class).forEach(itemInfoBpn -> {
				log.info("itemInfoBpn: {}", itemInfoBpn);

				if (Objects.isNull(itemInfoBpn)) {
					return;
				}

				Map<String, Object> context = getPurchaseAuditMessage(purchaseEntity,itemInfoBpn);
				if (context == null) {
					return;
				}

				// Trigger risk engine evaluation for point changes
				List<RcBlackListRuleEntity> rules = rcBlackListRuleEntityRepository.findByTriggerType(TRIGGER_TYPE);
				for (RcBlackListRuleEntity rcBlackListRuleEntity : rules) {
					List<RcScenceRuleEntity> rcScenceRuleEntities = rcScenceRuleEntityRepository.findByRuleId(rcBlackListRuleEntity.getId());
					for (RcScenceRuleEntity rcScenceRuleEntity : rcScenceRuleEntities) {
						riskEngineComponent.evaluateRules(rcScenceRuleEntity.getSceneId(), purchaseEntity.getCardNo(), context, "SYSTEM_AUTO", true);
					}
				}
			});
		});




	}

	private PurchaseEntity transform(TransactionDomainSpecific orderSpecific) {
		//todo如果有原订单号，通过原订单号去找是否有重复的
		PurchaseEntity purchase = purchaseDbService.findByTransactionId(orderSpecific.getTransactionID());
        if (Objects.isNull(purchase)) {
			return newTransaction(orderSpecific);
		}
		return renewTransaction(purchase,orderSpecific);

	}

	private PurchaseEntity newTransaction(TransactionDomainSpecific orderSpecific) {
		PurchaseEntity purchase = new PurchaseEntity();
		purchase.setCardNo(PoslogAnalyzeHelper.analyzeCustomerId(orderSpecific));

		purchase.setTransactionId(orderSpecific.getTransactionID());
		purchase.setPurchaseDate(DateUtil.convert(PoslogAnalyzeHelper.analyzeTransactionDate(orderSpecific)));

		//digital / physical
//		purchase.setChannel(PoslogAnalyzeHelper.analyzeChannel(orderSpecific));
		purchase.setChannel(PoslogAnalyzeHelper.analyzeSpecificChanel(orderSpecific));
		//在初始化订单的时候就有买的
		List<ItemInfoBpn> salePartItems =  PoslogAnalyzeHelper.analyzeItemInfos(orderSpecific, true, false);
		purchase.setSaleDetail(JSONUtil.toJsonStr(salePartItems));

		renewTransaction(purchase,orderSpecific);

		//todo 解析地址
		log.info("has save purchase {}",JSONUtil.toList(purchase.getReturnDetail(), ItemInfoBpn.class));
		return purchase;
	}
	private PurchaseEntity renewTransaction(PurchaseEntity purchase,TransactionDomainSpecific orderSpecific){
		//todo 线下如果分多笔退呢，所以要不就不合并了，还是分开存
//		 origin_transaction = transaction id？
		List<ItemInfoBpn> returnPartItems =  PoslogAnalyzeHelper.analyzeItemInfos(orderSpecific, false, true);
		purchase.setReturnDetail(JSONUtil.toJsonStr(returnPartItems));
        purchase.setOriginTransactionId(PoslogAnalyzeHelper.originTransactionIds(returnPartItems));
		purchase.setTotalPrice(PoslogAnalyzeHelper.analyzeTotalPrice(orderSpecific));
		purchase.setSalePrice(purchaseDbService.getSalePrice(orderSpecific));
		purchase.setReturnPrice(purchaseDbService.getReturnPrice(orderSpecific));
		purchase.setStatus(PoslogAnalyzeHelper.anaylyzeTransactionStatus(orderSpecific));
		purchase.setTransactionDate(PoslogAnalyzeHelper.analyzeTxMessageDate(orderSpecific));
		purchase.setUpdatedAt(new Date());
		return purchase;
	}

	public Map<String, Object> getPurchaseAuditMessage(PurchaseEntity purchaseEntityList,ItemInfoBpn itemInfoBpn) {


        Map<String, Object> context = new HashMap<>();
		context.put("sys_purchase_date", DateUtil.timestamp(purchaseEntityList.getPurchaseDate()));
		context.put("sys_purchase_channel", purchaseEntityList.getChannel());
		context.put("sys_sale_price", purchaseEntityList.getSalePrice());
		//todo saledetail里没数据了还要继续拼吗
		String saleDetail = purchaseEntityList.getSaleDetail();
		if (StringUtils.isEmpty(saleDetail)) {
			return context;
		}
		context.put("sys_item_quantity", itemInfoBpn.getQuantity());
		context.put("sys_item_price_amount", itemInfoBpn.getItemTotalPrice());
		return context;

	}


}
