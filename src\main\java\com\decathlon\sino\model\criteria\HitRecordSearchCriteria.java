package com.decathlon.sino.model.criteria;

import com.decathlon.sino.data.entity.QRcBlackListReasonHitEntity;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class HitRecordSearchCriteria extends SearchCriteria implements Criteria {
	private static final long serialVersionUID = 1L;

	private Long ruleId;
	private String cardNumber;
	private String objectId;
	private Long blackListId;

	@Override
	public Predicate toPredicate() {
		QRcBlackListReasonHitEntity hitRecord = QRcBlackListReasonHitEntity.rcBlackListReasonHitEntity;
		Predicate predicate = hitRecord.isNotNull();

		if (ruleId != null) {
			predicate = ExpressionUtils.and(predicate, hitRecord.ruleId.eq(ruleId));
		}

		if (StringUtils.isNotBlank(cardNumber)) {
			predicate = ExpressionUtils.and(predicate, hitRecord.objectId.eq(cardNumber));
		}

		if (StringUtils.isNotBlank(objectId)) {
			predicate = ExpressionUtils.and(predicate, hitRecord.objectId.eq(objectId));
		}

		if (blackListId != null) {
			predicate = ExpressionUtils.and(predicate, hitRecord.blackListId.eq(blackListId));
		}

		return predicate;
	}
}
