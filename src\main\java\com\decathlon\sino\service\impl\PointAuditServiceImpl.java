package com.decathlon.sino.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.decathlon.sino.common.exception.ServiceException;
import com.decathlon.sino.component.RiskEngineComponent;
import com.decathlon.sino.data.dao.RcBizScenceEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListReasonHitEntityRepository;
import com.decathlon.sino.data.dao.RcBlacklistGlobalEntityRepository;
import com.decathlon.sino.model.output.RiskEvaluationResult;
import com.decathlon.sino.model.output.RiskQueryResult;
import com.decathlon.sino.service.AuditService;
import com.decathlon.sino.service.impl.helper.PointRiskHelper;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 * 积分审计服务实现
 *
 * 专门处理积分相关的风险审计，包括：
 * - 积分获取风险评估
 * - 积分使用异常检测
 * - 积分作弊行为识别
 * - 积分账户风险管理
 *
 * 继承自 AduitProcessService，复用基础的风险查询和评估功能
 */
@Service
@Slf4j
public class PointAuditServiceImpl extends AduitProcessService implements AuditService {

	// 积分事件数据字段常量
	private static final String FIELD_POINT_AMOUNT = "pointAmount";
	private static final String FIELD_POINTS = "points";
	private static final String FIELD_POINT_ACTION = "pointAction";
	private static final String FIELD_ACTION_TYPE = "actionType";
	private static final String FIELD_POINT_SOURCE = "pointSource";
	private static final String FIELD_TIMESTAMP = "timestamp";
	private static final String FIELD_EVENT_TIMESTAMP = "eventTimestamp";

	// 积分金额等级常量
	private static final String LEVEL_INVALID = "INVALID";
	private static final String LEVEL_LOW = "LOW";
	private static final String LEVEL_MEDIUM = "MEDIUM";
	private static final String LEVEL_HIGH = "HIGH";
	private static final String LEVEL_VERY_HIGH = "VERY_HIGH";

	/**
	 * 构造函数 - 注入所需的依赖
	 */
	public PointAuditServiceImpl(
			RiskEngineComponent riskEngineComponent,
			RcBlackListEntityRepository rcBlackListEntityRepository,
			RcBlackListReasonHitEntityRepository rcBlackListReasonHitEntityRepository,
			RcBizScenceEntityRepository rcBizScenceEntityRepository,
			RcBlacklistGlobalEntityRepository rcGlobalBlackListEntityRepository) {
		super(riskEngineComponent, rcBlackListEntityRepository, rcBlackListReasonHitEntityRepository,
			  rcBizScenceEntityRepository, rcGlobalBlackListEntityRepository);
	}

	/**
	 * 积分风险检查
	 *
	 * @param sceneId 场景ID
	 * @param objectId 对象ID（如用户ID、订单ID等）
	 * @param eventData 事件数据（包含积分相关信息）
	 * @param operator 操作人
	 * @param enableAudit 是否启用审计模式
	 * @return 风险查询结果
	 */
	@Override
	public RiskQueryResult checkRisk(Long sceneId, String objectId, JSONObject eventData, String operator, Boolean enableAudit) {
		log.info("开始积分风险检查: sceneId={}, objectId={}, enableAudit={}", sceneId, objectId, enableAudit);

		try {
			// 验证积分事件数据
			validatePointEventData(eventData);

			if (Boolean.TRUE.equals(enableAudit)) {
				// 审计模式：先评估再返回结果
				log.debug("使用审计模式进行积分风险评估");
				RiskEvaluationResult evaluationResult = this.auditRisk(sceneId, objectId, eventData, operator, enableAudit);

				// 将评估结果转换为查询结果格式
				return convertEvaluationToQueryResult(evaluationResult);
			} else {
				// 直接查询模式：快速查询现有风险状态
				log.debug("使用直接查询模式检查积分风险");
				return super.checkRisk(sceneId, objectId);
			}
		} catch (Exception e) {
			log.error("积分风险检查失败: sceneId={}, objectId={}", sceneId, objectId, e);
			throw new ServiceException("积分风险检查失败", e);
		}
	}

	/**
	 * 积分风险审计评估
	 *
	 * @param sceneId 场景ID
	 * @param objectId 对象ID
	 * @param eventData 事件数据（包含积分相关信息）
	 * @param operator 操作人
	 * @param enableRecord 是否记录评估结果
	 * @return 风险评估结果
	 */
	@Override
	public RiskEvaluationResult auditRisk(Long sceneId, String objectId, JSONObject eventData, String operator, Boolean enableRecord) {
		log.info("开始积分风险审计评估: sceneId={}, objectId={}, enableRecord={}", sceneId, objectId, enableRecord);

		try {
			// 1. 验证积分事件数据
			validatePointEventData(eventData);

			// 2. 提取积分相关的上下文信息
			Map<String, Object> pointContext = extractPointContext(sceneId, objectId, eventData);
			log.debug("积分上下文提取完成: {}", pointContext);

			// 3. 调用父类的审计评估方法
			RiskEvaluationResult result = super.auditRisk(sceneId, objectId, pointContext, operator, enableRecord);

			// 4. 增强积分特定的风险信息
			enhancePointRiskInfo(result, eventData);

			log.info("积分风险审计评估完成: sceneId={}, objectId={}, hitCount={}",
					sceneId, objectId, result.getHitRuleCount());

			return result;
		} catch (Exception e) {
			log.error("积分风险审计评估失败: sceneId={}, objectId={}", sceneId, objectId, e);
			throw new ServiceException("积分风险审计评估失败", e);
		}
	}

	/**
	 * 验证积分事件数据
	 *
	 * @param eventData 事件数据
	 */
	private void validatePointEventData(JSONObject eventData) {
		if (eventData == null) {
			log.warn("积分事件数据为空");
			return;
		}

		// 验证必要的积分字段
		if (!eventData.containsKey(FIELD_POINT_AMOUNT) && !eventData.containsKey(FIELD_POINTS)) {
			log.warn("积分事件数据中缺少积分金额字段");
		}

		// 验证积分操作类型
		if (!eventData.containsKey(FIELD_POINT_ACTION) && !eventData.containsKey(FIELD_ACTION_TYPE)) {
			log.warn("积分事件数据中缺少操作类型字段");
		}

		log.debug("积分事件数据验证完成");
	}

	/**
	 * 提取积分相关的上下文信息
	 *
	 * @param sceneId 场景ID
	 * @param objectId 对象ID
	 * @param eventData 事件数据
	 * @return 积分上下文信息
	 */
	private Map<String, Object> extractPointContext(Long sceneId, String objectId, JSONObject eventData) {
		if (eventData == null) {
			log.warn("事件数据为空，使用空的积分上下文");
			return Map.of();
		}

		try {
			// 使用 PointRiskHelper 提取积分上下文
			Map<String, Object> context = PointRiskHelper.getContext(sceneId, objectId, eventData);
			log.debug("积分上下文提取成功: keys={}", context.keySet());

			// 添加额外的积分风险评估参数
			enrichPointContext(context, eventData);

			return context;
		} catch (Exception e) {
			log.error("积分上下文提取失败", e);
			return Map.of();
		}
	}

	/**
	 * 丰富积分上下文信息
	 *
	 * @param context 上下文信息
	 * @param eventData 事件数据
	 */
	private void enrichPointContext(Map<String, Object> context, JSONObject eventData) {
		// 添加积分金额相关信息
		if (eventData.containsKey(FIELD_POINT_AMOUNT)) {
			Object pointAmount = eventData.get(FIELD_POINT_AMOUNT);
			context.put(FIELD_POINT_AMOUNT, pointAmount);

			// 计算积分金额等级
			if (pointAmount instanceof Number) {
				double amount = ((Number) pointAmount).doubleValue();
				context.put("pointAmountLevel", calculatePointAmountLevel(amount));
			}
		}

		// 添加积分操作类型
		if (eventData.containsKey(FIELD_POINT_ACTION)) {
			context.put(FIELD_POINT_ACTION, eventData.getStr(FIELD_POINT_ACTION));
		} else if (eventData.containsKey(FIELD_ACTION_TYPE)) {
			context.put(FIELD_POINT_ACTION, eventData.getStr(FIELD_ACTION_TYPE));
		}

		// 添加积分来源信息
		if (eventData.containsKey(FIELD_POINT_SOURCE)) {
			context.put(FIELD_POINT_SOURCE, eventData.getStr(FIELD_POINT_SOURCE));
		}

		// 添加时间相关信息
		if (eventData.containsKey(FIELD_TIMESTAMP)) {
			context.put(FIELD_EVENT_TIMESTAMP, eventData.get(FIELD_TIMESTAMP));
		}

		log.debug("积分上下文信息丰富完成");
	}

	/**
	 * 计算积分金额等级
	 *
	 * @param amount 积分金额
	 * @return 积分等级
	 */
	private String calculatePointAmountLevel(double amount) {
		if (amount <= 0) {
			return LEVEL_INVALID;
		} else if (amount <= 100) {
			return LEVEL_LOW;
		} else if (amount <= 1000) {
			return LEVEL_MEDIUM;
		} else if (amount <= 10000) {
			return LEVEL_HIGH;
		} else {
			return LEVEL_VERY_HIGH;
		}
	}

	/**
	 * 增强积分特定的风险信息
	 *
	 * @param result 风险评估结果
	 * @param eventData 原始事件数据
	 */
	private void enhancePointRiskInfo(RiskEvaluationResult result, JSONObject eventData) {
		// 这里可以添加积分特定的风险信息增强逻辑
		// 例如：积分异常模式、积分获取频率、积分使用模式等

		if (eventData != null) {
			// 记录积分操作类型
			if (eventData.containsKey(FIELD_POINT_ACTION)) {
				log.debug("积分操作类型: {}", eventData.getStr(FIELD_POINT_ACTION));
			}

			// 记录积分金额
			if (eventData.containsKey(FIELD_POINT_AMOUNT)) {
				log.debug("积分金额: {}", eventData.get(FIELD_POINT_AMOUNT));
			}

			// 记录积分来源
			if (eventData.containsKey(FIELD_POINT_SOURCE)) {
				log.debug("积分来源: {}", eventData.getStr(FIELD_POINT_SOURCE));
			}

			// 可以在这里添加更多积分特定的风险分析逻辑
			// 例如：检测积分刷取模式、异常积分获取等
			analyzePointRiskPatterns(result, eventData);
		}
	}

	/**
	 * 分析积分风险模式
	 *
	 * @param result 风险评估结果
	 * @param eventData 事件数据
	 */
	private void analyzePointRiskPatterns(RiskEvaluationResult result, JSONObject eventData) {
		// 分析积分获取频率异常
		analyzePointFrequencyRisk(eventData);

		// 分析积分金额异常
		analyzePointAmountRisk(eventData);

		// 分析积分来源异常
		analyzePointSourceRisk(eventData);
	}

	/**
	 * 分析积分获取频率风险
	 */
	private void analyzePointFrequencyRisk(JSONObject eventData) {
		// 这里可以添加积分获取频率分析逻辑
		// 例如：短时间内大量积分获取、异常高频操作等
		log.debug("分析积分获取频率风险");
	}

	/**
	 * 分析积分金额风险
	 */
	private void analyzePointAmountRisk(JSONObject eventData) {
		// 这里可以添加积分金额分析逻辑
		// 例如：异常大额积分、金额模式异常等
		if (eventData.containsKey(FIELD_POINT_AMOUNT)) {
			Object amount = eventData.get(FIELD_POINT_AMOUNT);
			if (amount instanceof Number) {
				double pointAmount = ((Number) amount).doubleValue();
				if (pointAmount > 10000) {
					log.warn("检测到大额积分操作: {}", pointAmount);
				}
			}
		}
	}

	/**
	 * 分析积分来源风险
	 */
	private void analyzePointSourceRisk(JSONObject eventData) {
		// 这里可以添加积分来源分析逻辑
		// 例如：异常来源、可疑渠道等
		if (eventData.containsKey(FIELD_POINT_SOURCE)) {
			String source = eventData.getStr(FIELD_POINT_SOURCE);
			log.debug("积分来源分析: {}", source);
		}
	}

	/**
	 * 将评估结果转换为查询结果格式
	 *
	 * @param evaluationResult 评估结果
	 * @return 查询结果
	 */
	private RiskQueryResult convertEvaluationToQueryResult(RiskEvaluationResult evaluationResult) {
		RiskQueryResult queryResult = new RiskQueryResult();
		queryResult.setSceneId(evaluationResult.getSceneId());
		queryResult.setObjectId(evaluationResult.getObjectId());
		queryResult.setInBlacklist(evaluationResult.getInBlacklist());
		queryResult.setRiskLevel(evaluationResult.getCurrentRiskLevel());
		queryResult.setTotalScore(evaluationResult.getCurrentTotalScore());
		queryResult.setHitRuleCount(evaluationResult.getHitRuleCount());
		queryResult.setBlocked(evaluationResult.getBlocked());
		queryResult.setQueryTime(evaluationResult.getEvaluationTime());

		// 设置命中记录为空（审计模式下的新命中记录不包含在查询结果中）
		queryResult.setHitRecords(java.util.Collections.emptyList());
		queryResult.setLastHitTime(null);

		return queryResult;
	}
}
