package com.decathlon.sino.model.bo;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@JsonNaming(SnakeCaseStrategy.class)
@AllArgsConstructor
public class RuleDefinition {
	
	  private Long ruleId;
	  // DSL/脚本
	  private String expr;       
	  private BigDecimal point;
	  private List<ParamDef> params;

}
